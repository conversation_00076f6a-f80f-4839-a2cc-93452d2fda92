const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const { glob } = require('glob');
const chokidar = require('chokidar');
const si = require('systeminformation');
const Logger = require('../utils/logger');
const { ContextError } = require('../utils/error-handler');

class ContextManager {
  constructor(config) {
    this.config = config;
    this.logger = new Logger('ContextManager');
    this.context = {
      system: {},
      environment: {},
      workspace: {},
      files: {},
      processes: {},
      history: [],
      memory: new Map()
    };
    
    this.watchers = new Map();
    this.updateInterval = null;
    this.isInitialized = false;
  }

  async initialize() {
    try {
      this.logger.info('Initializing Context Manager...');
      
      // Load persistent context
      await this.loadPersistentContext();
      
      // Gather system information
      await this.gatherSystemInfo();
      
      // Start file watchers
      await this.setupFileWatchers();
      
      // Start periodic updates
      this.startPeriodicUpdates();
      
      this.isInitialized = true;
      this.logger.info('Context Manager initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize context manager:', error);
      throw new ContextError('Context manager initialization failed', error);
    }
  }

  async discoverEnvironment() {
    try {
      this.logger.info('Discovering environment...');
      
      const cwd = process.cwd();
      
      // Analyze current directory
      await this.analyzeWorkspace(cwd);
      
      // Detect project type
      await this.detectProjectType(cwd);
      
      // Index important files
      await this.indexFiles(cwd);
      
      // Gather environment variables
      this.gatherEnvironmentInfo();
      
      // Check running processes
      await this.gatherProcessInfo();
      
      this.logger.info('Environment discovery completed');
      
    } catch (error) {
      this.logger.error('Environment discovery failed:', error);
      throw new ContextError('Environment discovery failed', error);
    }
  }

  async gatherSystemInfo() {
    try {
      const [cpu, mem, osInfo, disk] = await Promise.all([
        si.cpu(),
        si.mem(),
        si.osInfo(),
        si.diskLayout()
      ]);

      this.context.system = {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        username: os.userInfo().username,
        homeDir: os.homedir(),
        tmpDir: os.tmpdir(),
        cpu: {
          manufacturer: cpu.manufacturer,
          brand: cpu.brand,
          cores: cpu.cores,
          speed: cpu.speed
        },
        memory: {
          total: mem.total,
          free: mem.free,
          used: mem.used
        },
        os: {
          distro: osInfo.distro,
          release: osInfo.release,
          kernel: osInfo.kernel,
          arch: osInfo.arch
        },
        disk: disk.map(d => ({
          device: d.device,
          size: d.size,
          type: d.type
        }))
      };

    } catch (error) {
      this.logger.warn('Failed to gather system info:', error);
    }
  }

  async analyzeWorkspace(workspacePath) {
    try {
      const stats = await fs.stat(workspacePath);
      
      this.context.workspace = {
        path: workspacePath,
        name: path.basename(workspacePath),
        isDirectory: stats.isDirectory(),
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        permissions: stats.mode,
        structure: await this.getDirectoryStructure(workspacePath)
      };

    } catch (error) {
      this.logger.warn('Failed to analyze workspace:', error);
    }
  }

  async detectProjectType(projectPath) {
    const indicators = {
      'package.json': 'nodejs',
      'requirements.txt': 'python',
      'Pipfile': 'python',
      'pyproject.toml': 'python',
      'Cargo.toml': 'rust',
      'go.mod': 'go',
      'pom.xml': 'java',
      'build.gradle': 'java',
      'composer.json': 'php',
      'Gemfile': 'ruby',
      '.csproj': 'dotnet',
      'CMakeLists.txt': 'cpp',
      'Makefile': 'c/cpp'
    };

    const detectedTypes = [];
    
    for (const [file, type] of Object.entries(indicators)) {
      const filePath = path.join(projectPath, file);
      if (await fs.pathExists(filePath)) {
        detectedTypes.push(type);
        
        // Read and parse configuration files
        try {
          if (file === 'package.json') {
            const packageJson = await fs.readJson(filePath);
            this.context.workspace.packageInfo = packageJson;
          }
        } catch (error) {
          this.logger.warn(`Failed to parse ${file}:`, error);
        }
      }
    }

    this.context.workspace.projectTypes = detectedTypes;
    this.context.workspace.primaryType = detectedTypes[0] || 'unknown';
  }

  async indexFiles(basePath, maxDepth = 3) {
    try {
      const patterns = [
        '**/*.js', '**/*.ts', '**/*.jsx', '**/*.tsx',
        '**/*.py', '**/*.rs', '**/*.go', '**/*.java',
        '**/*.cpp', '**/*.c', '**/*.h', '**/*.hpp',
        '**/*.md', '**/*.txt', '**/*.json', '**/*.yaml', '**/*.yml',
        '**/README*', '**/LICENSE*', '**/Dockerfile*',
        '**/.env*', '**/config.*'
      ];

      const files = await glob(patterns, {
        cwd: basePath,
        ignore: ['**/node_modules/**', '**/target/**', '**/.git/**', '**/dist/**', '**/build/**'],
        absolute: true,
        maxDepth
      });

      this.context.files = {
        total: files.length,
        byExtension: {},
        important: [],
        recent: []
      };

      // Categorize files
      for (const file of files) {
        const ext = path.extname(file);
        this.context.files.byExtension[ext] = (this.context.files.byExtension[ext] || 0) + 1;
        
        // Mark important files
        const basename = path.basename(file).toLowerCase();
        if (basename.includes('readme') || basename.includes('config') || basename.includes('main')) {
          this.context.files.important.push(file);
        }
      }

      // Get recently modified files
      const fileStats = await Promise.all(
        files.slice(0, 50).map(async file => {
          try {
            const stats = await fs.stat(file);
            return { file, mtime: stats.mtime };
          } catch {
            return null;
          }
        })
      );

      this.context.files.recent = fileStats
        .filter(Boolean)
        .sort((a, b) => b.mtime - a.mtime)
        .slice(0, 10)
        .map(item => item.file);

    } catch (error) {
      this.logger.warn('Failed to index files:', error);
    }
  }

  gatherEnvironmentInfo() {
    this.context.environment = {
      variables: {
        PATH: process.env.PATH,
        NODE_ENV: process.env.NODE_ENV,
        SHELL: process.env.SHELL,
        TERM: process.env.TERM,
        LANG: process.env.LANG
      },
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch
      },
      cwd: process.cwd(),
      argv: process.argv
    };
  }

  async gatherProcessInfo() {
    try {
      const processes = await si.processes();
      
      this.context.processes = {
        total: processes.all,
        running: processes.running,
        sleeping: processes.sleeping,
        list: processes.list
          .filter(p => p.name && !p.name.startsWith('['))
          .slice(0, 20)
          .map(p => ({
            pid: p.pid,
            name: p.name,
            cpu: p.cpu,
            mem: p.mem
          }))
      };

    } catch (error) {
      this.logger.warn('Failed to gather process info:', error);
    }
  }

  async getDirectoryStructure(dirPath, maxDepth = 2, currentDepth = 0) {
    if (currentDepth >= maxDepth) return null;

    try {
      const items = await fs.readdir(dirPath);
      const structure = {};

      for (const item of items.slice(0, 20)) { // Limit items
        if (item.startsWith('.') && !item.startsWith('.env')) continue;
        
        const itemPath = path.join(dirPath, item);
        const stats = await fs.stat(itemPath);
        
        if (stats.isDirectory()) {
          structure[item] = await this.getDirectoryStructure(itemPath, maxDepth, currentDepth + 1);
        } else {
          structure[item] = {
            size: stats.size,
            modified: stats.mtime
          };
        }
      }

      return structure;
    } catch (error) {
      return null;
    }
  }

  async setupFileWatchers() {
    const watchPaths = [
      process.cwd(),
      path.join(process.cwd(), 'package.json'),
      path.join(process.cwd(), '.env')
    ];

    for (const watchPath of watchPaths) {
      if (await fs.pathExists(watchPath)) {
        const watcher = chokidar.watch(watchPath, {
          ignored: /(^|[\/\\])\../, // ignore dotfiles
          persistent: true,
          ignoreInitial: true
        });

        watcher.on('change', (filePath) => {
          this.logger.debug('File changed:', filePath);
          this.handleFileChange(filePath);
        });

        this.watchers.set(watchPath, watcher);
      }
    }
  }

  handleFileChange(filePath) {
    // Update context when important files change
    if (path.basename(filePath) === 'package.json') {
      this.detectProjectType(path.dirname(filePath));
    }
    
    // Add to recent changes
    this.context.history.unshift({
      type: 'file_change',
      path: filePath,
      timestamp: new Date().toISOString()
    });

    // Keep history limited
    if (this.context.history.length > 100) {
      this.context.history = this.context.history.slice(0, 100);
    }
  }

  startPeriodicUpdates() {
    this.updateInterval = setInterval(async () => {
      try {
        await this.gatherProcessInfo();
        await this.gatherSystemInfo();
      } catch (error) {
        this.logger.warn('Periodic update failed:', error);
      }
    }, 30000); // Update every 30 seconds
  }

  async updateContext() {
    if (!this.isInitialized) return;
    
    try {
      // Quick updates that don't require full discovery
      this.gatherEnvironmentInfo();
      
      // Update workspace if CWD changed
      const currentCwd = process.cwd();
      if (this.context.workspace.path !== currentCwd) {
        await this.analyzeWorkspace(currentCwd);
        await this.detectProjectType(currentCwd);
      }
      
    } catch (error) {
      this.logger.warn('Context update failed:', error);
    }
  }

  async updateWithExecutionResult(result) {
    this.context.history.unshift({
      type: 'execution',
      result: result.summary,
      success: result.success,
      duration: result.duration,
      completedSteps: result.completedSteps,
      totalSteps: result.totalSteps,
      insights: result.insights || [],
      timestamp: new Date().toISOString()
    });

    // Keep history limited
    if (this.context.history.length > 100) {
      this.context.history = this.context.history.slice(0, 100);
    }

    // Update workspace context if files were modified
    if (result.results && result.results.some(r => r.tool === 'file-operations')) {
      await this.refreshFileContext();
    }

    // Save persistent context
    await this.savePersistentContext();
  }

  async addLearningData(learningData) {
    if (!this.context.learning) {
      this.context.learning = {
        patterns: [],
        toolPerformance: {},
        executionMetrics: [],
        insights: []
      };
    }

    // Add execution metrics
    this.context.learning.executionMetrics.push(learningData);

    // Update tool performance data
    Object.entries(learningData.toolUsage || {}).forEach(([tool, usage]) => {
      if (!this.context.learning.toolPerformance[tool]) {
        this.context.learning.toolPerformance[tool] = {
          totalUsage: 0,
          successRate: 0,
          avgExecutionTime: 0,
          lastUsed: null
        };
      }

      const toolData = this.context.learning.toolPerformance[tool];
      toolData.totalUsage += usage;
      toolData.lastUsed = learningData.timestamp;

      // Update success rate (simplified)
      if (learningData.successRate !== undefined) {
        toolData.successRate = (toolData.successRate + learningData.successRate) / 2;
      }
    });

    // Extract and store insights
    if (learningData.insights && learningData.insights.length > 0) {
      this.context.learning.insights.push(...learningData.insights.map(insight => ({
        ...insight,
        timestamp: learningData.timestamp
      })));
    }

    // Detect patterns
    await this.detectLearningPatterns();

    // Keep learning data manageable
    if (this.context.learning.executionMetrics.length > 50) {
      this.context.learning.executionMetrics = this.context.learning.executionMetrics.slice(0, 50);
    }

    if (this.context.learning.insights.length > 100) {
      this.context.learning.insights = this.context.learning.insights.slice(0, 100);
    }

    await this.savePersistentContext();
  }

  async detectLearningPatterns() {
    const metrics = this.context.learning.executionMetrics;
    if (metrics.length < 5) return; // Need minimum data for pattern detection

    const patterns = [];

    // Pattern 1: Tool preference based on project type
    const projectType = this.context.workspace.primaryType;
    if (projectType) {
      const toolUsageByProject = metrics.reduce((acc, metric) => {
        Object.entries(metric.toolUsage || {}).forEach(([tool, usage]) => {
          acc[tool] = (acc[tool] || 0) + usage;
        });
        return acc;
      }, {});

      const preferredTools = Object.entries(toolUsageByProject)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([tool]) => tool);

      patterns.push({
        type: 'tool_preference',
        projectType,
        preferredTools,
        confidence: 0.8,
        description: `For ${projectType} projects, commonly used tools are: ${preferredTools.join(', ')}`
      });
    }

    // Pattern 2: Execution time patterns
    const avgExecutionTime = metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length;
    const complexityCorrelation = metrics.map(m => ({
      complexity: m.planComplexity,
      time: m.executionTime
    }));

    if (complexityCorrelation.length >= 5) {
      const correlation = this.calculateCorrelation(
        complexityCorrelation.map(c => c.complexity),
        complexityCorrelation.map(c => c.time)
      );

      if (correlation > 0.7) {
        patterns.push({
          type: 'execution_time',
          correlation,
          avgTime: avgExecutionTime,
          description: `Strong correlation between plan complexity and execution time (r=${correlation.toFixed(2)})`
        });
      }
    }

    // Pattern 3: Success rate patterns
    const successRates = metrics.map(m => m.successRate).filter(sr => sr !== undefined);
    if (successRates.length >= 5) {
      const avgSuccessRate = successRates.reduce((sum, sr) => sum + sr, 0) / successRates.length;

      if (avgSuccessRate < 80) {
        patterns.push({
          type: 'success_rate',
          avgSuccessRate,
          level: 'warning',
          description: `Average success rate is below optimal (${avgSuccessRate.toFixed(1)}%)`
        });
      }
    }

    this.context.learning.patterns = patterns;
  }

  calculateCorrelation(x, y) {
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  }

  async refreshFileContext() {
    try {
      const currentPath = process.cwd();
      await this.analyzeWorkspace(currentPath);
      this.logger.debug('File context refreshed');
    } catch (error) {
      this.logger.warn('Failed to refresh file context:', error);
    }
  }

  async getContext() {
    return {
      ...this.context,
      timestamp: new Date().toISOString()
    };
  }

  async getContextSize() {
    return JSON.stringify(this.context).length;
  }

  async loadPersistentContext() {
    try {
      this.logger.debug('Loading persistent context...');

      const contextDir = path.join(os.homedir(), '.ai-cli', 'context');
      const contextFile = path.join(contextDir, 'persistent-context.json');

      if (await fs.pathExists(contextFile)) {
        const persistentData = await fs.readJson(contextFile);

        // Merge persistent data with current context
        if (persistentData.learning) {
          this.context.learning = persistentData.learning;
        }

        if (persistentData.memory) {
          this.context.memory = new Map(persistentData.memory);
        }

        if (persistentData.preferences) {
          this.context.preferences = persistentData.preferences;
        }

        // Load recent history (last 20 items)
        if (persistentData.history && Array.isArray(persistentData.history)) {
          this.context.history = persistentData.history.slice(0, 20);
        }

        this.logger.debug('Persistent context loaded successfully');
      } else {
        this.logger.debug('No persistent context found, starting fresh');
      }
    } catch (error) {
      this.logger.warn('Failed to load persistent context:', error);
    }
  }

  async savePersistentContext() {
    try {
      const contextDir = path.join(os.homedir(), '.ai-cli', 'context');
      const contextFile = path.join(contextDir, 'persistent-context.json');

      await fs.ensureDir(contextDir);

      const persistentData = {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        learning: this.context.learning || {},
        memory: Array.from(this.context.memory.entries()),
        preferences: this.context.preferences || {},
        history: this.context.history.slice(0, 50), // Save last 50 history items
        workspace: {
          lastPath: this.context.workspace.path,
          lastProjectType: this.context.workspace.primaryType
        }
      };

      await fs.writeJson(contextFile, persistentData, { spaces: 2 });
      this.logger.debug('Persistent context saved');
    } catch (error) {
      this.logger.warn('Failed to save persistent context:', error);
    }
  }

  async getSystemInfo() {
    return this.context.system;
  }

  async healthCheck() {
    try {
      // Check if context is properly initialized
      if (!this.isInitialized) return false;

      // Check if we can access the workspace
      if (this.context.workspace.path) {
        const exists = await fs.pathExists(this.context.workspace.path);
        if (!exists) return false;
      }

      // Check if watchers are working
      if (this.watchers.size === 0 && this.config.enableFileWatching !== false) {
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('Context health check failed:', error);
      return false;
    }
  }

  async exportContext(outputPath, format = 'json') {
    try {
      const exportData = {
        timestamp: new Date().toISOString(),
        context: this.context,
        metadata: {
          version: '1.0.0',
          platform: process.platform,
          nodeVersion: process.version
        }
      };

      let output;
      if (format === 'json') {
        output = JSON.stringify(exportData, null, 2);
      } else if (format === 'yaml') {
        const yaml = require('yaml');
        output = yaml.stringify(exportData);
      } else {
        throw new Error(`Unsupported export format: ${format}`);
      }

      await fs.writeFile(outputPath, output, 'utf8');
      this.logger.info(`Context exported to: ${outputPath}`);

      return outputPath;
    } catch (error) {
      this.logger.error('Failed to export context:', error);
      throw error;
    }
  }

  async importContext(inputPath) {
    try {
      const content = await fs.readFile(inputPath, 'utf8');
      let importData;

      if (inputPath.endsWith('.json')) {
        importData = JSON.parse(content);
      } else if (inputPath.endsWith('.yaml') || inputPath.endsWith('.yml')) {
        const yaml = require('yaml');
        importData = yaml.parse(content);
      } else {
        throw new Error('Unsupported import format. Use .json or .yaml files.');
      }

      // Merge imported context
      if (importData.context) {
        this.context = {
          ...this.context,
          ...importData.context,
          // Preserve current system and workspace info
          system: this.context.system,
          workspace: this.context.workspace
        };
      }

      this.logger.info(`Context imported from: ${inputPath}`);
      await this.savePersistentContext();

    } catch (error) {
      this.logger.error('Failed to import context:', error);
      throw error;
    }
  }



  destroy() {
    // Clean up watchers and intervals
    for (const watcher of this.watchers.values()) {
      watcher.close();
    }
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
  }
}

module.exports = ContextManager;
