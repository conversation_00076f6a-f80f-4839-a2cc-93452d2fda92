const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const EventEmitter = require('events');
const Logger = require('../utils/logger');
const { PluginError } = require('../utils/error-handler');

class PluginManager extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = config;
    this.logger = new Logger('PluginManager');
    this.plugins = new Map();
    this.pluginPaths = [
      path.join(process.cwd(), 'plugins'),
      path.join(os.homedir(), '.ai-cli', 'plugins'),
      path.join(__dirname, '..', 'plugins')
    ];
    this.isInitialized = false;
  }

  async initialize() {
    try {
      this.logger.info('Initializing plugin manager...');
      
      // Ensure plugin directories exist
      for (const pluginPath of this.pluginPaths) {
        await fs.ensureDir(pluginPath);
      }
      
      // Load plugins from all paths
      await this.loadPlugins();
      
      this.isInitialized = true;
      this.logger.info(`Plugin manager initialized with ${this.plugins.size} plugins`);
      
    } catch (error) {
      this.logger.error('Plugin manager initialization failed:', error);
      throw new PluginError('Plugin manager initialization failed', error);
    }
  }

  async loadPlugins() {
    for (const pluginPath of this.pluginPaths) {
      try {
        if (await fs.pathExists(pluginPath)) {
          await this.loadPluginsFromDirectory(pluginPath);
        }
      } catch (error) {
        this.logger.warn(`Failed to load plugins from ${pluginPath}:`, error);
      }
    }
  }

  async loadPluginsFromDirectory(directory) {
    const entries = await fs.readdir(directory, { withFileTypes: true });
    
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const pluginDir = path.join(directory, entry.name);
        await this.loadPlugin(pluginDir);
      } else if (entry.name.endsWith('.js')) {
        const pluginFile = path.join(directory, entry.name);
        await this.loadPlugin(pluginFile);
      }
    }
  }

  async loadPlugin(pluginPath) {
    try {
      let pluginModule;
      let pluginManifest = {};
      
      // Check if it's a directory with package.json
      if ((await fs.stat(pluginPath)).isDirectory()) {
        const manifestPath = path.join(pluginPath, 'package.json');
        const mainPath = path.join(pluginPath, 'index.js');
        
        if (await fs.pathExists(manifestPath)) {
          pluginManifest = await fs.readJson(manifestPath);
        }
        
        if (await fs.pathExists(mainPath)) {
          pluginModule = require(mainPath);
        } else {
          this.logger.warn(`Plugin directory ${pluginPath} missing index.js`);
          return;
        }
      } else {
        // Single file plugin
        pluginModule = require(pluginPath);
        pluginManifest.name = path.basename(pluginPath, '.js');
      }
      
      // Validate plugin
      const plugin = await this.validateAndInitializePlugin(pluginModule, pluginManifest, pluginPath);
      
      // Register plugin
      this.plugins.set(plugin.name, plugin);
      this.logger.info(`Loaded plugin: ${plugin.name} v${plugin.version}`);
      
      // Emit plugin loaded event
      this.emit('pluginLoaded', plugin);
      
    } catch (error) {
      this.logger.error(`Failed to load plugin from ${pluginPath}:`, error);
    }
  }

  async validateAndInitializePlugin(pluginModule, manifest, pluginPath) {
    // Extract plugin class or function
    const PluginClass = pluginModule.default || pluginModule;
    
    if (typeof PluginClass !== 'function') {
      throw new PluginError('Plugin must export a class or constructor function');
    }
    
    // Create plugin instance
    const pluginInstance = new PluginClass();
    
    // Validate required properties
    if (!pluginInstance.name) {
      pluginInstance.name = manifest.name || path.basename(pluginPath);
    }
    
    if (!pluginInstance.version) {
      pluginInstance.version = manifest.version || '1.0.0';
    }
    
    if (!pluginInstance.description) {
      pluginInstance.description = manifest.description || 'No description provided';
    }
    
    // Validate required methods
    if (typeof pluginInstance.initialize !== 'function') {
      pluginInstance.initialize = async () => {};
    }
    
    if (typeof pluginInstance.execute !== 'function') {
      throw new PluginError('Plugin must implement execute method');
    }
    
    // Add metadata
    pluginInstance.path = pluginPath;
    pluginInstance.manifest = manifest;
    pluginInstance.loadedAt = new Date().toISOString();
    
    // Initialize plugin
    await pluginInstance.initialize();
    
    return pluginInstance;
  }

  async executePlugin(pluginName, parameters = {}) {
    if (!this.plugins.has(pluginName)) {
      throw new PluginError(`Plugin not found: ${pluginName}`);
    }
    
    const plugin = this.plugins.get(pluginName);
    
    try {
      this.logger.debug(`Executing plugin: ${pluginName}`);
      
      // Execute plugin
      const result = await plugin.execute(parameters);
      
      // Emit execution event
      this.emit('pluginExecuted', {
        plugin: pluginName,
        parameters,
        result,
        timestamp: new Date().toISOString()
      });
      
      return result;
      
    } catch (error) {
      this.logger.error(`Plugin execution failed: ${pluginName}`, error);
      
      // Emit error event
      this.emit('pluginError', {
        plugin: pluginName,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      throw new PluginError(`Plugin execution failed: ${error.message}`, error);
    }
  }

  getPlugin(name) {
    return this.plugins.get(name);
  }

  getAllPlugins() {
    return Array.from(this.plugins.values());
  }

  getPluginsByCategory(category) {
    return this.getAllPlugins().filter(plugin => plugin.category === category);
  }

  async unloadPlugin(pluginName) {
    if (!this.plugins.has(pluginName)) {
      throw new PluginError(`Plugin not found: ${pluginName}`);
    }
    
    const plugin = this.plugins.get(pluginName);
    
    try {
      // Call cleanup if available
      if (typeof plugin.cleanup === 'function') {
        await plugin.cleanup();
      }
      
      // Remove from registry
      this.plugins.delete(pluginName);
      
      // Clear from require cache if it's a file
      if (plugin.path && require.cache[plugin.path]) {
        delete require.cache[plugin.path];
      }
      
      this.logger.info(`Unloaded plugin: ${pluginName}`);
      
      // Emit unload event
      this.emit('pluginUnloaded', pluginName);
      
    } catch (error) {
      this.logger.error(`Failed to unload plugin ${pluginName}:`, error);
      throw new PluginError(`Plugin unload failed: ${error.message}`, error);
    }
  }

  async reloadPlugin(pluginName) {
    const plugin = this.plugins.get(pluginName);
    if (!plugin) {
      throw new PluginError(`Plugin not found: ${pluginName}`);
    }
    
    const pluginPath = plugin.path;
    
    // Unload plugin
    await this.unloadPlugin(pluginName);
    
    // Reload plugin
    await this.loadPlugin(pluginPath);
    
    this.logger.info(`Reloaded plugin: ${pluginName}`);
  }

  async installPlugin(source) {
    // Support different installation sources
    if (source.startsWith('http://') || source.startsWith('https://')) {
      return await this.installFromUrl(source);
    } else if (source.includes('/')) {
      return await this.installFromGit(source);
    } else {
      return await this.installFromNpm(source);
    }
  }

  async installFromUrl(url) {
    // Download and install plugin from URL
    const axios = require('axios');
    const pluginDir = path.join(this.pluginPaths[1], 'downloaded');
    
    await fs.ensureDir(pluginDir);
    
    const response = await axios.get(url);
    const pluginName = path.basename(url, '.js');
    const pluginPath = path.join(pluginDir, `${pluginName}.js`);
    
    await fs.writeFile(pluginPath, response.data);
    await this.loadPlugin(pluginPath);
    
    return pluginName;
  }

  async installFromNpm(packageName) {
    // Install plugin from npm
    const { spawn } = require('child_process');
    const pluginDir = path.join(this.pluginPaths[1], 'npm');
    
    await fs.ensureDir(pluginDir);
    
    return new Promise((resolve, reject) => {
      const npm = spawn('npm', ['install', packageName], {
        cwd: pluginDir,
        stdio: 'pipe'
      });
      
      npm.on('close', async (code) => {
        if (code === 0) {
          try {
            const packagePath = path.join(pluginDir, 'node_modules', packageName);
            await this.loadPlugin(packagePath);
            resolve(packageName);
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new PluginError(`npm install failed with code ${code}`));
        }
      });
    });
  }

  async createPlugin(name, template = 'basic') {
    const pluginDir = path.join(this.pluginPaths[0], name);
    await fs.ensureDir(pluginDir);
    
    const templates = {
      basic: this.getBasicPluginTemplate(name),
      tool: this.getToolPluginTemplate(name),
      command: this.getCommandPluginTemplate(name)
    };
    
    const pluginCode = templates[template] || templates.basic;
    
    // Create plugin files
    await fs.writeFile(path.join(pluginDir, 'index.js'), pluginCode);
    await fs.writeFile(path.join(pluginDir, 'package.json'), JSON.stringify({
      name: `ai-cli-plugin-${name}`,
      version: '1.0.0',
      description: `AI CLI plugin: ${name}`,
      main: 'index.js',
      keywords: ['ai-cli', 'plugin'],
      author: 'AI CLI User'
    }, null, 2));
    
    this.logger.info(`Created plugin: ${name} at ${pluginDir}`);
    return pluginDir;
  }

  getBasicPluginTemplate(name) {
    return `class ${name.charAt(0).toUpperCase() + name.slice(1)}Plugin {
  constructor() {
    this.name = '${name}';
    this.version = '1.0.0';
    this.description = 'A basic AI CLI plugin';
    this.category = 'general';
  }

  async initialize() {
    // Plugin initialization code
    console.log(\`\${this.name} plugin initialized\`);
  }

  async execute(parameters = {}) {
    // Plugin execution code
    return {
      success: true,
      message: \`\${this.name} plugin executed successfully\`,
      parameters
    };
  }

  async cleanup() {
    // Plugin cleanup code
    console.log(\`\${this.name} plugin cleaned up\`);
  }
}

module.exports = ${name.charAt(0).toUpperCase() + name.slice(1)}Plugin;`;
  }

  getToolPluginTemplate(name) {
    return `class ${name.charAt(0).toUpperCase() + name.slice(1)}Tool {
  constructor() {
    this.name = '${name}';
    this.version = '1.0.0';
    this.description = 'A tool plugin for AI CLI';
    this.category = 'tools';
    
    this.schema = {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          description: 'Action to perform'
        }
      },
      required: ['action']
    };
  }

  async initialize() {
    console.log(\`\${this.name} tool plugin initialized\`);
  }

  async execute(parameters = {}) {
    const { action } = parameters;
    
    // Tool execution logic
    return {
      success: true,
      action,
      result: \`Executed \${action} successfully\`,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = ${name.charAt(0).toUpperCase() + name.slice(1)}Tool;`;
  }

  getCommandPluginTemplate(name) {
    return `class ${name.charAt(0).toUpperCase() + name.slice(1)}Command {
  constructor() {
    this.name = '${name}';
    this.version = '1.0.0';
    this.description = 'A command plugin for AI CLI';
    this.category = 'commands';
    this.command = '${name}';
    this.options = [
      {
        flags: '-v, --verbose',
        description: 'Verbose output'
      }
    ];
  }

  async initialize() {
    console.log(\`\${this.name} command plugin initialized\`);
  }

  async execute(parameters = {}) {
    const { verbose = false } = parameters;
    
    if (verbose) {
      console.log(\`Executing \${this.name} command with verbose output\`);
    }
    
    // Command execution logic
    return {
      success: true,
      command: this.command,
      output: \`\${this.name} command executed successfully\`,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = ${name.charAt(0).toUpperCase() + name.slice(1)}Command;`;
  }

  async healthCheck() {
    const results = {};
    
    for (const [name, plugin] of this.plugins) {
      try {
        if (typeof plugin.healthCheck === 'function') {
          results[name] = await plugin.healthCheck();
        } else {
          results[name] = true; // Assume healthy if no health check
        }
      } catch (error) {
        this.logger.error(`Plugin health check failed: ${name}`, error);
        results[name] = false;
      }
    }
    
    return results;
  }

  getPluginStats() {
    const stats = {
      total: this.plugins.size,
      byCategory: {},
      byStatus: { active: 0, inactive: 0 }
    };
    
    for (const plugin of this.plugins.values()) {
      // Count by category
      const category = plugin.category || 'uncategorized';
      stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
      
      // Count by status (simplified)
      stats.byStatus.active++;
    }
    
    return stats;
  }
}

module.exports = PluginManager;
