const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const inquirer = require('inquirer');
const chalk = require('chalk');
const yaml = require('yaml');
const Logger = require('../utils/logger');
const { ConfigError } = require('../utils/error-handler');

class ConfigManager {
  constructor() {
    this.logger = new Logger('ConfigManager');
    this.configDir = path.join(os.homedir(), '.ai-cli');
    this.configFile = path.join(this.configDir, 'config.yaml');
    this.defaultConfig = this.getDefaultConfig();
  }

  getDefaultConfig() {
    return {
      version: '1.0.0',
      llm: {
        primaryProvider: 'openai',
        fallbackProviders: ['anthropic', 'ollama'],
        openai: {
          apiKey: null,
          baseURL: null,
          defaultModel: 'gpt-4',
          maxTokens: 4000,
          temperature: 0.7
        },
        anthropic: {
          apiKey: null,
          defaultModel: 'claude-3-sonnet-20240229',
          maxTokens: 4000,
          temperature: 0.7
        },
        gemini: {
          apiKey: null,
          defaultModel: 'gemini-pro',
          maxTokens: 4000,
          temperature: 0.7
        },
        ollama: {
          enabled: false,
          host: 'http://localhost:11434',
          models: ['llama2', 'codellama', 'mistral'],
          defaultModel: 'llama2'
        },
        deepseek: {
          apiKey: null,
          defaultModel: 'deepseek-chat',
          maxTokens: 4000,
          temperature: 0.7
        },
        mistral: {
          apiKey: null,
          defaultModel: 'mistral-large',
          maxTokens: 4000,
          temperature: 0.7
        }
      },
      context: {
        maxHistorySize: 100,
        autoDiscovery: true,
        watchFiles: true,
        indexDepth: 3,
        excludePatterns: [
          '**/node_modules/**',
          '**/target/**',
          '**/.git/**',
          '**/dist/**',
          '**/build/**',
          '**/.next/**',
          '**/.nuxt/**'
        ]
      },
      execution: {
        maxConcurrency: 3,
        timeout: 300000,
        continueOnError: false,
        confirmDangerous: true,
        logLevel: 'info'
      },
      tools: {
        enableAll: true,
        customToolsPath: './ai-cli-tools',
        fileOperations: {
          enabled: true,
          maxFileSize: 10485760, // 10MB
          allowedExtensions: [
            '.js', '.ts', '.jsx', '.tsx', '.py', '.rs', '.go',
            '.java', '.cpp', '.c', '.h', '.hpp', '.md', '.txt',
            '.json', '.yaml', '.yml', '.xml', '.csv', '.sql'
          ]
        },
        shellCommands: {
          enabled: true,
          allowDangerous: false,
          timeout: 30000,
          maxOutputSize: 1048576 // 1MB
        },
        systemInfo: {
          enabled: true,
          includeProcesses: true,
          includeNetwork: true
        }
      },
      ui: {
        theme: 'default',
        showBanner: true,
        verboseOutput: false,
        confirmActions: true,
        autoSave: true
      },
      security: {
        allowFileSystemAccess: true,
        allowNetworkAccess: true,
        allowProcessExecution: true,
        restrictedPaths: [
          '/etc/passwd',
          '/etc/shadow',
          '/etc/hosts',
          'C:\\Windows\\System32',
          'C:\\Windows\\SysWOW64'
        ],
        maxExecutionTime: 600000, // 10 minutes
        requireConfirmation: [
          'delete',
          'remove',
          'format',
          'shutdown',
          'reboot'
        ]
      }
    };
  }

  async load() {
    try {
      // Ensure config directory exists
      await fs.ensureDir(this.configDir);

      // Load existing config or create default
      if (await fs.pathExists(this.configFile)) {
        const configContent = await fs.readFile(this.configFile, 'utf8');
        const userConfig = yaml.parse(configContent);
        
        // Merge with defaults to ensure all properties exist
        const config = this.mergeConfigs(this.defaultConfig, userConfig);
        
        // Validate configuration
        this.validateConfig(config);
        
        this.logger.debug('Configuration loaded successfully');
        return config;
      } else {
        this.logger.info('No configuration found, creating default config');
        await this.save(this.defaultConfig);
        return this.defaultConfig;
      }
    } catch (error) {
      this.logger.error('Failed to load configuration:', error);
      throw new ConfigError('Configuration loading failed', error);
    }
  }

  async save(config) {
    try {
      await fs.ensureDir(this.configDir);
      
      // Validate before saving
      this.validateConfig(config);
      
      const configContent = yaml.stringify(config, {
        indent: 2,
        lineWidth: 120
      });
      
      await fs.writeFile(this.configFile, configContent, 'utf8');
      this.logger.debug('Configuration saved successfully');
    } catch (error) {
      this.logger.error('Failed to save configuration:', error);
      throw new ConfigError('Configuration saving failed', error);
    }
  }

  async runSetupWizard() {
    console.log(chalk.cyan('\n🔧 AI CLI Configuration Setup\n'));
    
    try {
      const config = await this.load();
      
      // LLM Provider Setup
      console.log(chalk.yellow('📡 LLM Provider Configuration'));
      
      const { primaryProvider } = await inquirer.prompt([
        {
          type: 'list',
          name: 'primaryProvider',
          message: 'Select your primary LLM provider:',
          choices: [
            { name: 'OpenAI (GPT-4, GPT-3.5)', value: 'openai' },
            { name: 'Anthropic (Claude)', value: 'anthropic' },
            { name: 'Google (Gemini)', value: 'gemini' },
            { name: 'DeepSeek', value: 'deepseek' },
            { name: 'Mistral', value: 'mistral' },
            { name: 'Ollama (Local)', value: 'ollama' }
          ],
          default: config.llm.primaryProvider
        }
      ]);
      
      config.llm.primaryProvider = primaryProvider;
      
      // Configure API keys for selected provider
      await this.configureProvider(config, primaryProvider);
      
      // Fallback providers
      const { fallbackProviders } = await inquirer.prompt([
        {
          type: 'checkbox',
          name: 'fallbackProviders',
          message: 'Select fallback providers (optional):',
          choices: [
            { name: 'OpenAI', value: 'openai' },
            { name: 'Anthropic', value: 'anthropic' },
            { name: 'Google Gemini', value: 'gemini' },
            { name: 'DeepSeek', value: 'deepseek' },
            { name: 'Mistral', value: 'mistral' },
            { name: 'Ollama', value: 'ollama' }
          ].filter(choice => choice.value !== primaryProvider)
        }
      ]);
      
      config.llm.fallbackProviders = fallbackProviders;
      
      // Configure fallback providers
      for (const provider of fallbackProviders) {
        await this.configureProvider(config, provider);
      }
      
      // Execution Settings
      console.log(chalk.yellow('\n⚙️ Execution Configuration'));
      
      const executionSettings = await inquirer.prompt([
        {
          type: 'number',
          name: 'maxConcurrency',
          message: 'Maximum concurrent operations:',
          default: config.execution.maxConcurrency,
          validate: (value) => value > 0 && value <= 10
        },
        {
          type: 'confirm',
          name: 'continueOnError',
          message: 'Continue execution when errors occur?',
          default: config.execution.continueOnError
        },
        {
          type: 'confirm',
          name: 'confirmDangerous',
          message: 'Require confirmation for dangerous operations?',
          default: config.execution.confirmDangerous
        }
      ]);
      
      Object.assign(config.execution, executionSettings);
      
      // Security Settings
      console.log(chalk.yellow('\n🔒 Security Configuration'));
      
      const securitySettings = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'allowFileSystemAccess',
          message: 'Allow file system operations?',
          default: config.security.allowFileSystemAccess
        },
        {
          type: 'confirm',
          name: 'allowProcessExecution',
          message: 'Allow shell command execution?',
          default: config.security.allowProcessExecution
        },
        {
          type: 'confirm',
          name: 'allowNetworkAccess',
          message: 'Allow network operations?',
          default: config.security.allowNetworkAccess
        }
      ]);
      
      Object.assign(config.security, securitySettings);
      
      // Save configuration
      await this.save(config);
      
      console.log(chalk.green('\n✅ Configuration setup completed!'));
      console.log(chalk.gray(`Configuration saved to: ${this.configFile}`));
      
      return config;
      
    } catch (error) {
      console.error(chalk.red('\n❌ Setup failed:'), error.message);
      throw new ConfigError('Setup wizard failed', error);
    }
  }

  async configureProvider(config, provider) {
    console.log(chalk.blue(`\n🔑 Configuring ${provider.toUpperCase()}`));
    
    switch (provider) {
      case 'openai':
        const openaiConfig = await inquirer.prompt([
          {
            type: 'password',
            name: 'apiKey',
            message: 'OpenAI API Key:',
            mask: '*',
            when: !config.llm.openai.apiKey
          },
          {
            type: 'list',
            name: 'defaultModel',
            message: 'Default model:',
            choices: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
            default: config.llm.openai.defaultModel
          }
        ]);
        Object.assign(config.llm.openai, openaiConfig);
        break;
        
      case 'anthropic':
        const anthropicConfig = await inquirer.prompt([
          {
            type: 'password',
            name: 'apiKey',
            message: 'Anthropic API Key:',
            mask: '*',
            when: !config.llm.anthropic.apiKey
          },
          {
            type: 'list',
            name: 'defaultModel',
            message: 'Default model:',
            choices: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
            default: config.llm.anthropic.defaultModel
          }
        ]);
        Object.assign(config.llm.anthropic, anthropicConfig);
        break;
        
      case 'gemini':
        const geminiConfig = await inquirer.prompt([
          {
            type: 'password',
            name: 'apiKey',
            message: 'Google AI API Key:',
            mask: '*',
            when: !config.llm.gemini.apiKey
          }
        ]);
        Object.assign(config.llm.gemini, geminiConfig);
        break;
        
      case 'deepseek':
        const deepseekConfig = await inquirer.prompt([
          {
            type: 'password',
            name: 'apiKey',
            message: 'DeepSeek API Key:',
            mask: '*',
            when: !config.llm.deepseek.apiKey
          }
        ]);
        Object.assign(config.llm.deepseek, deepseekConfig);
        break;
        
      case 'mistral':
        const mistralConfig = await inquirer.prompt([
          {
            type: 'password',
            name: 'apiKey',
            message: 'Mistral API Key:',
            mask: '*',
            when: !config.llm.mistral.apiKey
          }
        ]);
        Object.assign(config.llm.mistral, mistralConfig);
        break;
        
      case 'ollama':
        const ollamaConfig = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'enabled',
            message: 'Enable Ollama (local LLM)?',
            default: config.llm.ollama.enabled
          },
          {
            type: 'input',
            name: 'host',
            message: 'Ollama host URL:',
            default: config.llm.ollama.host,
            when: (answers) => answers.enabled
          }
        ]);
        Object.assign(config.llm.ollama, ollamaConfig);
        break;
    }
  }

  mergeConfigs(defaultConfig, userConfig) {
    const merged = JSON.parse(JSON.stringify(defaultConfig));
    
    function deepMerge(target, source) {
      for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          if (!target[key]) target[key] = {};
          deepMerge(target[key], source[key]);
        } else {
          target[key] = source[key];
        }
      }
    }
    
    deepMerge(merged, userConfig);
    return merged;
  }

  validateConfig(config) {
    // Basic validation
    if (!config || typeof config !== 'object') {
      throw new ConfigError('Invalid configuration format');
    }
    
    if (!config.llm || !config.llm.primaryProvider) {
      throw new ConfigError('Primary LLM provider must be specified');
    }
    
    // Validate provider configuration
    const provider = config.llm.primaryProvider;
    if (!config.llm[provider]) {
      throw new ConfigError(`Configuration for provider '${provider}' not found`);
    }
    
    // Validate API keys for cloud providers
    const cloudProviders = ['openai', 'anthropic', 'gemini', 'deepseek', 'mistral'];
    if (cloudProviders.includes(provider) && !config.llm[provider].apiKey) {
      this.logger.warn(`API key not configured for provider: ${provider}`);
    }
    
    // Validate execution settings
    if (config.execution.maxConcurrency < 1 || config.execution.maxConcurrency > 10) {
      throw new ConfigError('maxConcurrency must be between 1 and 10');
    }
    
    if (config.execution.timeout < 1000) {
      throw new ConfigError('timeout must be at least 1000ms');
    }
  }

  async reset() {
    try {
      await this.save(this.defaultConfig);
      this.logger.info('Configuration reset to defaults');
    } catch (error) {
      throw new ConfigError('Failed to reset configuration', error);
    }
  }

  async backup() {
    try {
      const backupFile = path.join(this.configDir, `config.backup.${Date.now()}.yaml`);
      await fs.copy(this.configFile, backupFile);
      this.logger.info(`Configuration backed up to: ${backupFile}`);
      return backupFile;
    } catch (error) {
      throw new ConfigError('Failed to backup configuration', error);
    }
  }

  getConfigPath() {
    return this.configFile;
  }

  getConfigDir() {
    return this.configDir;
  }
}

module.exports = new ConfigManager();
