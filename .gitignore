# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# AI CLI specific files
.ai-cli/
logs/
temp/
cache/
backups/

# Configuration files with sensitive data
config.yaml
config.json
*.key
*.pem
*.p12
*.pfx

# Build outputs
dist/
build/
lib/
pkg/

# Test outputs
test-results/
coverage/
.nyc_output/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# Temporary files
*.tmp
*.temp
*.log
*.bak
*.backup

# Package files
*.tar.gz
*.zip
*.rar
*.7z

# Database files
*.db
*.sqlite
*.sqlite3

# AI CLI runtime files
execution-*.json
context-*.json
session-*.json

# Custom tools
ai-cli-tools/
plugins/

# Documentation build
docs/build/
docs/.docusaurus/

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local development
.local/
.cache/

# Runtime configuration
runtime-config.json
user-preferences.json

# Performance logs
perf-*.log

# Memory dumps
*.heapsnapshot

# Error dumps
error-*.dump

# Profiling data
profile-*.cpuprofile

# Security scan results
security-scan-*.json

# Audit logs
audit-*.log

# Backup files
*.backup.*
backup-*

# Archive files
archive/
archives/

# Generated documentation
api-docs/
generated-docs/

# Compiled assets
assets/compiled/
public/compiled/

# Webpack bundles
webpack-stats.json
webpack-bundle-analyzer-report.html

# Rollup bundles
rollup-stats.json

# Parcel bundles
parcel-bundle-reports/

# ESBuild outputs
esbuild-meta.json

# SWC outputs
.swc/

# Turborepo
.turbo/

# Vercel
.vercel

# Netlify
.netlify/

# Sentry
.sentryclirc

# Local Netlify folder
.netlify

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Supabase
.supabase/

# Prisma
prisma/migrations/
.env.local

# Planetscale
.pscale/

# Railway
.railway/

# Fly.io
fly.toml

# Docker
.dockerignore
Dockerfile.local
docker-compose.override.yml

# Kubernetes
k8s-local/
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible
*.retry

# Vagrant
.vagrant/

# Pulumi
Pulumi.*.yaml
.pulumi/

# CDK
cdk.out/
cdk.context.json

# Serverless Framework
.serverless/
serverless.yml.local

# AWS
.aws/

# Google Cloud
.gcloud/
service-account-key.json

# Azure
.azure/

# Digital Ocean
.do/

# Heroku
.heroku/

# Custom ignore patterns
# Add your custom patterns here
