const OpenAI = require('openai');
const Anthropic = require('@anthropic-ai/sdk');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { Ollama } = require('ollama');
const axios = require('axios');
const Logger = require('../utils/logger');
const { LLMError } = require('../utils/error-handler');

class LLMManager {
  constructor(config) {
    this.config = config;
    this.logger = new Logger('LLMManager');
    this.providers = new Map();
    this.currentProvider = null;
    this.fallbackProviders = [];
    
    this.initializeProviders();
  }

  async initialize() {
    this.logger.info('Initializing LLM Manager...');
    
    // Set primary provider
    this.currentProvider = this.config.primaryProvider || 'openai';
    
    // Set fallback chain
    this.fallbackProviders = this.config.fallbackProviders || ['anthropic', 'ollama'];
    
    // Test primary provider
    await this.testProvider(this.currentProvider);
    
    this.logger.info(`LLM Manager initialized with provider: ${this.currentProvider}`);
  }

  initializeProviders() {
    // OpenAI
    if (this.config.openai?.apiKey) {
      this.providers.set('openai', {
        client: new OpenAI({
          apiKey: this.config.openai.apiKey,
          baseURL: this.config.openai.baseURL
        }),
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        defaultModel: this.config.openai.defaultModel || 'gpt-4'
      });
    }

    // Anthropic
    if (this.config.anthropic?.apiKey) {
      this.providers.set('anthropic', {
        client: new Anthropic({
          apiKey: this.config.anthropic.apiKey
        }),
        models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
        defaultModel: this.config.anthropic.defaultModel || 'claude-3-sonnet'
      });
    }

    // Google Gemini
    if (this.config.gemini?.apiKey) {
      this.providers.set('gemini', {
        client: new GoogleGenerativeAI(this.config.gemini.apiKey),
        models: ['gemini-pro', 'gemini-pro-vision'],
        defaultModel: this.config.gemini.defaultModel || 'gemini-pro'
      });
    }

    // Ollama (local)
    if (this.config.ollama?.enabled) {
      this.providers.set('ollama', {
        client: new Ollama({
          host: this.config.ollama.host || 'http://localhost:11434'
        }),
        models: this.config.ollama.models || ['llama2', 'codellama', 'mistral'],
        defaultModel: this.config.ollama.defaultModel || 'llama2'
      });
    }

    // DeepSeek
    if (this.config.deepseek?.apiKey) {
      this.providers.set('deepseek', {
        client: new OpenAI({
          apiKey: this.config.deepseek.apiKey,
          baseURL: 'https://api.deepseek.com/v1'
        }),
        models: ['deepseek-chat', 'deepseek-coder'],
        defaultModel: this.config.deepseek.defaultModel || 'deepseek-chat'
      });
    }

    // Mistral
    if (this.config.mistral?.apiKey) {
      this.providers.set('mistral', {
        client: axios.create({
          baseURL: 'https://api.mistral.ai/v1',
          headers: {
            'Authorization': `Bearer ${this.config.mistral.apiKey}`,
            'Content-Type': 'application/json'
          }
        }),
        models: ['mistral-large', 'mistral-medium', 'mistral-small'],
        defaultModel: this.config.mistral.defaultModel || 'mistral-large'
      });
    }
  }

  async generateResponse(prompt, options = {}) {
    const maxRetries = 3;
    let lastError = null;

    // Try primary provider first
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await this.callProvider(this.currentProvider, prompt, options);
      } catch (error) {
        lastError = error;
        this.logger.warn(`Attempt ${attempt + 1} failed for ${this.currentProvider}:`, error.message);
        
        if (attempt < maxRetries - 1) {
          await this.delay(1000 * (attempt + 1)); // Exponential backoff
        }
      }
    }

    // Try fallback providers
    for (const fallbackProvider of this.fallbackProviders) {
      if (this.providers.has(fallbackProvider)) {
        try {
          this.logger.info(`Falling back to provider: ${fallbackProvider}`);
          const response = await this.callProvider(fallbackProvider, prompt, options);
          
          // Temporarily switch to working provider
          this.currentProvider = fallbackProvider;
          return response;
          
        } catch (error) {
          this.logger.warn(`Fallback provider ${fallbackProvider} failed:`, error.message);
          lastError = error;
        }
      }
    }

    throw new LLMError('All LLM providers failed', lastError);
  }

  async callProvider(providerName, prompt, options = {}) {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new LLMError(`Provider ${providerName} not configured`);
    }

    const model = options.model || provider.defaultModel;
    const maxTokens = options.maxTokens || 4000;
    const temperature = options.temperature || 0.7;

    switch (providerName) {
      case 'openai':
      case 'deepseek':
        return await this.callOpenAICompatible(provider.client, model, prompt, {
          maxTokens,
          temperature,
          ...options
        });

      case 'anthropic':
        return await this.callAnthropic(provider.client, model, prompt, {
          maxTokens,
          temperature,
          ...options
        });

      case 'gemini':
        return await this.callGemini(provider.client, model, prompt, {
          maxTokens,
          temperature,
          ...options
        });

      case 'ollama':
        return await this.callOllama(provider.client, model, prompt, {
          maxTokens,
          temperature,
          ...options
        });

      case 'mistral':
        return await this.callMistral(provider.client, model, prompt, {
          maxTokens,
          temperature,
          ...options
        });

      default:
        throw new LLMError(`Unknown provider: ${providerName}`);
    }
  }

  async callOpenAICompatible(client, model, prompt, options) {
    const response = await client.chat.completions.create({
      model,
      messages: [
        {
          role: 'system',
          content: 'You are an autonomous AI agent assistant. Provide helpful, accurate, and actionable responses.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: options.maxTokens,
      temperature: options.temperature,
      stream: false
    });

    return response.choices[0].message.content;
  }

  async callAnthropic(client, model, prompt, options) {
    const response = await client.messages.create({
      model,
      max_tokens: options.maxTokens,
      temperature: options.temperature,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    });

    return response.content[0].text;
  }

  async callGemini(client, model, prompt, options) {
    const genModel = client.getGenerativeModel({ model });
    const result = await genModel.generateContent(prompt);
    const response = await result.response;
    return response.text();
  }

  async callOllama(client, model, prompt, options) {
    const response = await client.chat({
      model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      stream: false,
      options: {
        temperature: options.temperature,
        num_predict: options.maxTokens
      }
    });

    return response.message.content;
  }

  async callMistral(client, model, prompt, options) {
    try {
      const response = await client.post('/chat/completions', {
        model,
        messages: [
          {
            role: 'system',
            content: 'You are an autonomous AI agent assistant. Provide helpful, accurate, and actionable responses.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        stream: false
      });

      if (!response.data || !response.data.choices || !response.data.choices[0]) {
        throw new LLMError('Invalid response format from Mistral API');
      }

      return response.data.choices[0].message.content;
    } catch (error) {
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.error?.message || error.response.statusText;
        throw new LLMError(`Mistral API error (${status}): ${message}`, error);
      }
      throw new LLMError('Mistral API request failed', error);
    }
  }

  async testProvider(providerName) {
    try {
      await this.callProvider(providerName, 'Hello, please respond with "OK" to confirm connectivity.', {
        maxTokens: 10
      });
      this.logger.debug(`Provider ${providerName} test successful`);
      return true;
    } catch (error) {
      this.logger.warn(`Provider ${providerName} test failed:`, error.message);
      return false;
    }
  }

  async healthCheck() {
    return await this.testProvider(this.currentProvider);
  }

  getCurrentProvider() {
    return this.currentProvider;
  }

  getCurrentModel() {
    const provider = this.providers.get(this.currentProvider);
    return provider?.defaultModel || 'unknown';
  }

  getAvailableProviders() {
    return Array.from(this.providers.keys());
  }

  switchProvider(providerName) {
    if (this.providers.has(providerName)) {
      this.currentProvider = providerName;
      this.logger.info(`Switched to provider: ${providerName}`);
      return true;
    }
    return false;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Advanced features
  async generateStreamingResponse(prompt, options = {}, onChunk = null) {
    const provider = this.providers.get(this.currentProvider);
    if (!provider) {
      throw new LLMError(`Provider ${this.currentProvider} not configured`);
    }

    const model = options.model || provider.defaultModel;
    const maxTokens = options.maxTokens || this.config[this.currentProvider]?.maxTokens || 4000;
    const temperature = options.temperature || this.config[this.currentProvider]?.temperature || 0.7;

    switch (this.currentProvider) {
      case 'openai':
      case 'deepseek':
        return await this.streamOpenAICompatible(provider.client, model, prompt, {
          maxTokens,
          temperature,
          onChunk,
          ...options
        });
      default:
        // Fallback to non-streaming for unsupported providers
        const response = await this.generateResponse(prompt, options);
        if (onChunk) onChunk(response);
        return response;
    }
  }

  async streamOpenAICompatible(client, model, prompt, options) {
    const stream = await client.chat.completions.create({
      model,
      messages: [
        {
          role: 'system',
          content: 'You are an autonomous AI agent assistant. Provide helpful, accurate, and actionable responses.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: options.maxTokens,
      temperature: options.temperature,
      stream: true
    });

    let fullResponse = '';
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        fullResponse += content;
        if (options.onChunk) {
          options.onChunk(content);
        }
      }
    }

    return fullResponse;
  }

  async getModelInfo(providerName = null, modelName = null) {
    const provider = providerName || this.currentProvider;
    const model = modelName || this.providers.get(provider)?.defaultModel;

    const modelInfo = {
      provider,
      model,
      capabilities: {
        streaming: ['openai', 'deepseek'].includes(provider),
        functionCalling: ['openai', 'anthropic', 'deepseek'].includes(provider),
        vision: ['gpt-4-vision', 'gemini-pro-vision'].includes(model),
        codeGeneration: ['deepseek-coder', 'codellama'].includes(model)
      },
      limits: {
        maxTokens: this.config[provider]?.maxTokens || 4000,
        contextWindow: this.getContextWindow(provider, model),
        rateLimit: this.getRateLimit(provider)
      }
    };

    return modelInfo;
  }

  getContextWindow(provider, model) {
    const contextWindows = {
      'gpt-4': 8192,
      'gpt-4-turbo': 128000,
      'gpt-3.5-turbo': 4096,
      'claude-3-opus': 200000,
      'claude-3-sonnet': 200000,
      'claude-3-haiku': 200000,
      'gemini-pro': 32768,
      'deepseek-chat': 32768,
      'deepseek-coder': 16384,
      'mistral-large': 32768,
      'llama2': 4096,
      'codellama': 16384
    };

    return contextWindows[model] || 4096;
  }

  getRateLimit(provider) {
    const rateLimits = {
      openai: { requests: 3500, tokens: 90000 },
      anthropic: { requests: 1000, tokens: 100000 },
      gemini: { requests: 1500, tokens: 32000 },
      deepseek: { requests: 1000, tokens: 50000 },
      mistral: { requests: 1000, tokens: 50000 },
      ollama: { requests: Infinity, tokens: Infinity }
    };

    return rateLimits[provider] || { requests: 1000, tokens: 50000 };
  }

  async estimateTokens(text) {
    // Simple token estimation (roughly 4 characters per token)
    return Math.ceil(text.length / 4);
  }

  async validatePrompt(prompt, maxTokens = null) {
    const estimatedTokens = await this.estimateTokens(prompt);
    const modelInfo = await this.getModelInfo();
    const limit = maxTokens || modelInfo.limits.maxTokens;

    if (estimatedTokens > limit) {
      throw new LLMError(`Prompt too long: ${estimatedTokens} tokens exceeds limit of ${limit}`);
    }

    return { estimatedTokens, limit, valid: true };
  }

  // Provider management
  async refreshProviderStatus() {
    const status = {};

    for (const [name, provider] of this.providers) {
      try {
        const isHealthy = await this.testProvider(name);
        const modelInfo = await this.getModelInfo(name);

        status[name] = {
          healthy: isHealthy,
          model: provider.defaultModel,
          capabilities: modelInfo.capabilities,
          lastTested: new Date().toISOString()
        };
      } catch (error) {
        status[name] = {
          healthy: false,
          error: error.message,
          lastTested: new Date().toISOString()
        };
      }
    }

    return status;
  }

  async optimizeProviderSelection(prompt, requirements = {}) {
    const promptTokens = await this.estimateTokens(prompt);
    const availableProviders = Array.from(this.providers.keys());

    const scores = {};

    for (const provider of availableProviders) {
      const modelInfo = await this.getModelInfo(provider);
      let score = 0;

      // Context window score
      if (promptTokens <= modelInfo.limits.contextWindow) {
        score += 10;
      }

      // Capability matching
      if (requirements.streaming && modelInfo.capabilities.streaming) score += 5;
      if (requirements.functionCalling && modelInfo.capabilities.functionCalling) score += 5;
      if (requirements.vision && modelInfo.capabilities.vision) score += 5;
      if (requirements.codeGeneration && modelInfo.capabilities.codeGeneration) score += 5;

      // Provider reliability (based on recent health checks)
      const isHealthy = await this.testProvider(provider);
      if (isHealthy) score += 3;

      scores[provider] = score;
    }

    // Return best provider
    const bestProvider = Object.keys(scores).reduce((a, b) =>
      scores[a] > scores[b] ? a : b
    );

    return {
      recommended: bestProvider,
      scores,
      reasoning: this.generateProviderRecommendationReason(bestProvider, scores, requirements)
    };
  }

  generateProviderRecommendationReason(provider, scores, requirements) {
    const reasons = [];
    const modelInfo = this.getModelInfo(provider);

    if (scores[provider] >= 15) {
      reasons.push('Excellent match for requirements');
    } else if (scores[provider] >= 10) {
      reasons.push('Good match for requirements');
    } else {
      reasons.push('Basic compatibility');
    }

    if (requirements.streaming && modelInfo.capabilities.streaming) {
      reasons.push('Supports streaming');
    }

    if (requirements.codeGeneration && modelInfo.capabilities.codeGeneration) {
      reasons.push('Optimized for code generation');
    }

    return reasons.join(', ');
  }
}

module.exports = LLMManager;
