const os = require('os');
const fs = require('fs-extra');
const path = require('path');
const si = require('systeminformation');
const Logger = require('../utils/logger');
const { ToolError } = require('../utils/error-handler');

class SystemInfo {
  constructor() {
    this.name = 'system-info';
    this.description = 'Gather comprehensive system information including hardware, software, processes, and network details';
    this.category = 'system';
    this.logger = new Logger('SystemInfo');
    this.executionCount = 0;
    this.lastUsed = null;
    
    this.schema = {
      type: 'object',
      properties: {
        category: {
          type: 'string',
          enum: [
            'all', 'basic', 'hardware', 'software', 'network', 
            'processes', 'memory', 'disk', 'cpu', 'gpu', 
            'battery', 'temperature', 'services'
          ],
          description: 'Category of system information to gather',
          default: 'basic'
        },
        detailed: {
          type: 'boolean',
          description: 'Whether to include detailed information',
          default: false
        },
        realtime: {
          type: 'boolean',
          description: 'Whether to include real-time metrics',
          default: false
        }
      }
    };

    this.examples = [
      {
        description: 'Get basic system information',
        parameters: { category: 'basic' }
      },
      {
        description: 'Get detailed hardware information',
        parameters: { category: 'hardware', detailed: true }
      },
      {
        description: 'Get real-time system metrics',
        parameters: { category: 'all', realtime: true }
      }
    ];

    this.keywords = ['system', 'hardware', 'software', 'cpu', 'memory', 'disk', 'network', 'processes'];
  }

  async initialize() {
    try {
      this.logger.debug('Initializing SystemInfo tool...');

      // Simple initialization - don't run system queries during init
      this.logger.debug('SystemInfo tool initialized successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize SystemInfo tool:', error);
      throw error;
    }
  }

  async execute(parameters) {
    this.executionCount++;
    this.lastUsed = new Date().toISOString();
    
    const startTime = Date.now();
    
    try {
      const { category = 'basic', detailed = false, realtime = false } = parameters;
      
      this.logger.debug(`Gathering system information: ${category}`);
      
      let result;
      
      switch (category) {
        case 'all':
          result = await this.getAllSystemInfo(detailed, realtime);
          break;
        case 'basic':
          result = await this.getBasicSystemInfo();
          break;
        case 'hardware':
          result = await this.getHardwareInfo(detailed);
          break;
        case 'software':
          result = await this.getSoftwareInfo(detailed);
          break;
        case 'network':
          result = await this.getNetworkInfo(detailed);
          break;
        case 'processes':
          result = await this.getProcessInfo(detailed);
          break;
        case 'memory':
          result = await this.getMemoryInfo(detailed, realtime);
          break;
        case 'disk':
          result = await this.getDiskInfo(detailed);
          break;
        case 'cpu':
          result = await this.getCPUInfo(detailed, realtime);
          break;
        case 'gpu':
          result = await this.getGPUInfo(detailed);
          break;
        case 'battery':
          result = await this.getBatteryInfo();
          break;
        case 'temperature':
          result = await this.getTemperatureInfo();
          break;
        case 'services':
          result = await this.getServicesInfo();
          break;
        default:
          throw new ToolError(`Unknown system info category: ${category}`);
      }
      
      const executionTime = Date.now() - startTime;
      this.updateExecutionStats(executionTime);
      
      return {
        success: true,
        category,
        timestamp: new Date().toISOString(),
        result,
        executionTime
      };
      
    } catch (error) {
      this.logger.error('System info gathering failed:', error);
      throw new ToolError(`System info gathering failed: ${error.message}`, error);
    }
  }

  async getBasicSystemInfo() {
    const [osInfo, cpu, mem] = await Promise.all([
      si.osInfo(),
      si.cpu(),
      si.mem()
    ]);

    return {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      username: os.userInfo().username,
      uptime: os.uptime(),
      loadavg: os.loadavg(),
      os: {
        distro: osInfo.distro,
        release: osInfo.release,
        kernel: osInfo.kernel,
        arch: osInfo.arch,
        platform: osInfo.platform
      },
      cpu: {
        manufacturer: cpu.manufacturer,
        brand: cpu.brand,
        family: cpu.family,
        model: cpu.model,
        cores: cpu.cores,
        physicalCores: cpu.physicalCores,
        speed: cpu.speed
      },
      memory: {
        total: mem.total,
        free: mem.free,
        used: mem.used,
        available: mem.available,
        usage: ((mem.used / mem.total) * 100).toFixed(2) + '%'
      },
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        uptime: process.uptime()
      }
    };
  }

  async getHardwareInfo(detailed = false) {
    const promises = [
      si.cpu(),
      si.mem(),
      si.diskLayout(),
      si.graphics(),
      si.networkInterfaces()
    ];

    if (detailed) {
      promises.push(
        si.baseboard(),
        si.chassis(),
        si.bios(),
        si.system(),
        si.memLayout(),
        si.cpuCache(),
        si.cpuCurrentSpeed(),
        si.cpuTemperature()
      );
    }

    const results = await Promise.all(promises);
    const [cpu, mem, disk, graphics, network] = results;

    const hardware = {
      cpu,
      memory: mem,
      disk,
      graphics,
      network
    };

    if (detailed) {
      const [baseboard, chassis, bios, system, memLayout, cpuCache, cpuSpeed, cpuTemp] = results.slice(5);
      hardware.baseboard = baseboard;
      hardware.chassis = chassis;
      hardware.bios = bios;
      hardware.system = system;
      hardware.memoryLayout = memLayout;
      hardware.cpuCache = cpuCache;
      hardware.cpuSpeed = cpuSpeed;
      hardware.cpuTemperature = cpuTemp;
    }

    return hardware;
  }

  async getSoftwareInfo(detailed = false) {
    const [osInfo, versions] = await Promise.all([
      si.osInfo(),
      si.versions()
    ]);

    const software = {
      os: osInfo,
      versions,
      environment: {
        node: process.version,
        npm: process.env.npm_version,
        shell: process.env.SHELL,
        term: process.env.TERM,
        lang: process.env.LANG
      }
    };

    if (detailed) {
      try {
        const [users, services] = await Promise.all([
          si.users(),
          si.services('*')
        ]);
        software.users = users;
        software.services = services.slice(0, 20); // Limit to first 20 services
      } catch (error) {
        this.logger.warn('Failed to get detailed software info:', error);
      }
    }

    return software;
  }

  async getNetworkInfo(detailed = false) {
    const [interfaces, connections] = await Promise.all([
      si.networkInterfaces(),
      si.networkConnections()
    ]);

    const network = {
      interfaces,
      connections: connections.slice(0, 50) // Limit connections
    };

    if (detailed) {
      try {
        const [stats, gateway] = await Promise.all([
          si.networkStats(),
          si.networkGatewayDefault()
        ]);
        network.stats = stats;
        network.gateway = gateway;
      } catch (error) {
        this.logger.warn('Failed to get detailed network info:', error);
      }
    }

    return network;
  }

  async getProcessInfo(detailed = false) {
    const processes = await si.processes();

    const processInfo = {
      all: processes.all,
      running: processes.running,
      sleeping: processes.sleeping,
      blocked: processes.blocked,
      zombie: processes.zombie,
      list: processes.list.slice(0, detailed ? 100 : 20)
    };

    if (detailed) {
      // Get current process info
      processInfo.current = {
        pid: process.pid,
        ppid: process.ppid,
        title: process.title,
        argv: process.argv,
        execPath: process.execPath,
        cwd: process.cwd(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      };
    }

    return processInfo;
  }

  async getMemoryInfo(detailed = false, realtime = false) {
    const mem = await si.mem();

    const memoryInfo = {
      total: mem.total,
      free: mem.free,
      used: mem.used,
      active: mem.active,
      available: mem.available,
      buffers: mem.buffers,
      cached: mem.cached,
      slab: mem.slab,
      buffcache: mem.buffcache,
      swaptotal: mem.swaptotal,
      swapused: mem.swapused,
      swapfree: mem.swapfree
    };

    if (detailed) {
      try {
        const memLayout = await si.memLayout();
        memoryInfo.layout = memLayout;
      } catch (error) {
        this.logger.warn('Failed to get memory layout:', error);
      }
    }

    if (realtime) {
      // Get multiple samples for real-time data
      const samples = [];
      for (let i = 0; i < 5; i++) {
        const sample = await si.mem();
        samples.push({
          timestamp: Date.now(),
          used: sample.used,
          free: sample.free,
          usage: (sample.used / sample.total * 100).toFixed(2)
        });
        if (i < 4) await this.delay(1000);
      }
      memoryInfo.realtime = samples;
    }

    return memoryInfo;
  }

  async getDiskInfo(detailed = false) {
    const [diskLayout, fsSize, blockDevices] = await Promise.all([
      si.diskLayout(),
      si.fsSize(),
      si.blockDevices()
    ]);

    const diskInfo = {
      layout: diskLayout,
      filesystems: fsSize,
      blockDevices
    };

    if (detailed) {
      try {
        const [fsStats, disksIO] = await Promise.all([
          si.fsStats(),
          si.disksIO()
        ]);
        diskInfo.stats = fsStats;
        diskInfo.io = disksIO;
      } catch (error) {
        this.logger.warn('Failed to get detailed disk info:', error);
      }
    }

    return diskInfo;
  }

  async getCPUInfo(detailed = false, realtime = false) {
    const [cpu, cpuSpeed, cpuTemp] = await Promise.all([
      si.cpu(),
      si.cpuCurrentSpeed(),
      si.cpuTemperature()
    ]);

    const cpuInfo = {
      ...cpu,
      currentSpeed: cpuSpeed,
      temperature: cpuTemp
    };

    if (detailed) {
      try {
        const [cpuCache, cpuFlags] = await Promise.all([
          si.cpuCache(),
          si.cpuFlags()
        ]);
        cpuInfo.cache = cpuCache;
        cpuInfo.flags = cpuFlags;
      } catch (error) {
        this.logger.warn('Failed to get detailed CPU info:', error);
      }
    }

    if (realtime) {
      // Get CPU load samples
      const samples = [];
      for (let i = 0; i < 5; i++) {
        const load = await si.currentLoad();
        samples.push({
          timestamp: Date.now(),
          currentLoad: load.currentLoad,
          currentLoadUser: load.currentLoadUser,
          currentLoadSystem: load.currentLoadSystem,
          currentLoadIdle: load.currentLoadIdle
        });
        if (i < 4) await this.delay(1000);
      }
      cpuInfo.realtime = samples;
    }

    return cpuInfo;
  }

  async getGPUInfo(detailed = false) {
    try {
      const graphics = await si.graphics();
      
      if (detailed) {
        // Try to get additional GPU info if available
        try {
          const displays = await si.graphics();
          return {
            controllers: graphics.controllers,
            displays: displays.displays
          };
        } catch (error) {
          this.logger.warn('Failed to get detailed GPU info:', error);
        }
      }
      
      return graphics;
    } catch (error) {
      this.logger.warn('Failed to get GPU info:', error);
      return { error: 'GPU information not available' };
    }
  }

  async getBatteryInfo() {
    try {
      const battery = await si.battery();
      return battery;
    } catch (error) {
      this.logger.warn('Failed to get battery info:', error);
      return { error: 'Battery information not available' };
    }
  }

  async getTemperatureInfo() {
    try {
      const [cpuTemp, sensors] = await Promise.all([
        si.cpuTemperature(),
        si.sensors()
      ]);
      
      return {
        cpu: cpuTemp,
        sensors
      };
    } catch (error) {
      this.logger.warn('Failed to get temperature info:', error);
      return { error: 'Temperature information not available' };
    }
  }

  async getServicesInfo() {
    try {
      const services = await si.services('*');
      return {
        total: services.length,
        running: services.filter(s => s.state === 'running').length,
        stopped: services.filter(s => s.state === 'stopped').length,
        services: services.slice(0, 50) // Limit to first 50 services
      };
    } catch (error) {
      this.logger.warn('Failed to get services info:', error);
      return { error: 'Services information not available' };
    }
  }

  async getAllSystemInfo(detailed = false, realtime = false) {
    const [basic, hardware, software, network, processes, memory, disk, cpu] = await Promise.all([
      this.getBasicSystemInfo(),
      this.getHardwareInfo(detailed),
      this.getSoftwareInfo(detailed),
      this.getNetworkInfo(detailed),
      this.getProcessInfo(detailed),
      this.getMemoryInfo(detailed, realtime),
      this.getDiskInfo(detailed),
      this.getCPUInfo(detailed, realtime)
    ]);

    const allInfo = {
      basic,
      hardware,
      software,
      network,
      processes,
      memory,
      disk,
      cpu
    };

    if (detailed) {
      try {
        const [gpu, battery, temperature, services] = await Promise.all([
          this.getGPUInfo(true),
          this.getBatteryInfo(),
          this.getTemperatureInfo(),
          this.getServicesInfo()
        ]);
        
        allInfo.gpu = gpu;
        allInfo.battery = battery;
        allInfo.temperature = temperature;
        allInfo.services = services;
      } catch (error) {
        this.logger.warn('Failed to get additional system info:', error);
      }
    }

    return allInfo;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  updateExecutionStats(executionTime) {
    if (!this.averageExecutionTime) {
      this.averageExecutionTime = executionTime;
    } else {
      this.averageExecutionTime = (this.averageExecutionTime + executionTime) / 2;
    }
  }

  async healthCheck() {
    try {
      // Test basic system info gathering
      const basic = await this.getBasicSystemInfo();
      return basic && basic.platform && basic.hostname;
    } catch (error) {
      this.logger.error('System info health check failed:', error);
      return false;
    }
  }
}

module.exports = SystemInfo;
