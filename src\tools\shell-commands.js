const { spawn, exec } = require('child_process');
const { promisify } = require('util');
const os = require('os');
const path = require('path');
const which = require('which');
const Logger = require('../utils/logger');
const { ToolError } = require('../utils/error-handler');

const execAsync = promisify(exec);

class ShellCommands {
  constructor() {
    this.name = 'shell-commands';
    this.description = 'Execute shell commands and system operations with full access to the command line';
    this.category = 'system';
    this.logger = new Logger('ShellCommands');
    this.executionCount = 0;
    this.lastUsed = null;
    
    this.schema = {
      type: 'object',
      properties: {
        command: {
          type: 'string',
          description: 'The shell command to execute'
        },
        args: {
          type: 'array',
          items: { type: 'string' },
          description: 'Command arguments (alternative to including in command string)'
        },
        cwd: {
          type: 'string',
          description: 'Working directory for command execution',
          default: process.cwd()
        },
        env: {
          type: 'object',
          description: 'Environment variables for the command'
        },
        timeout: {
          type: 'number',
          description: 'Command timeout in milliseconds',
          default: 30000
        },
        shell: {
          type: 'string',
          description: 'Shell to use for command execution'
        },
        interactive: {
          type: 'boolean',
          description: 'Whether to run command interactively',
          default: false
        },
        captureOutput: {
          type: 'boolean',
          description: 'Whether to capture command output',
          default: true
        },
        allowDangerous: {
          type: 'boolean',
          description: 'Allow potentially dangerous commands',
          default: false
        }
      },
      required: ['command']
    };

    this.examples = [
      {
        description: 'List directory contents',
        parameters: { command: 'ls -la' }
      },
      {
        description: 'Check Node.js version',
        parameters: { command: 'node --version' }
      },
      {
        description: 'Install npm package',
        parameters: { command: 'npm install lodash', cwd: './my-project' }
      }
    ];

    this.keywords = ['shell', 'command', 'execute', 'run', 'terminal', 'bash', 'cmd', 'powershell'];
    
    // Dangerous commands that require explicit permission
    this.dangerousCommands = [
      'rm -rf', 'del /f', 'format', 'fdisk', 'mkfs',
      'dd if=', 'sudo rm', 'sudo del', 'shutdown', 'reboot',
      'halt', 'poweroff', 'init 0', 'init 6', 'systemctl poweroff',
      'systemctl reboot', 'chmod 777', 'chown -R', 'passwd',
      'userdel', 'groupdel', 'crontab -r', 'history -c'
    ];
  }

  async initialize() {
    try {
      this.logger.debug('Initializing ShellCommands tool...');

      // Simple initialization - don't run actual commands during init
      this.logger.debug('ShellCommands tool initialized successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize ShellCommands tool:', error);
      throw error;
    }
  }

  async execute(parameters) {
    this.executionCount++;
    this.lastUsed = new Date().toISOString();
    
    const startTime = Date.now();
    
    try {
      const {
        command,
        args = [],
        cwd = process.cwd(),
        env = {},
        timeout = 30000,
        shell,
        interactive = false,
        captureOutput = true,
        allowDangerous = false
      } = parameters;
      
      this.logger.debug(`Executing shell command: ${command}`);
      
      // Security validation
      this.validateCommand(command, allowDangerous);
      
      // Prepare environment
      const commandEnv = { ...process.env, ...env };
      
      let result;
      
      if (interactive) {
        result = await this.executeInteractive(command, args, { cwd, env: commandEnv, shell });
      } else {
        result = await this.executeNonInteractive(command, args, {
          cwd,
          env: commandEnv,
          timeout,
          shell,
          captureOutput
        });
      }
      
      const executionTime = Date.now() - startTime;
      this.updateExecutionStats(executionTime);
      
      return {
        success: true,
        command,
        result,
        executionTime,
        cwd
      };
      
    } catch (error) {
      this.logger.error('Shell command execution failed:', error);
      throw new ToolError(`Shell command failed: ${error.message}`, error);
    }
  }

  validateCommand(command, allowDangerous) {
    if (!command || typeof command !== 'string') {
      throw new ToolError('Invalid command');
    }
    
    // Check for dangerous commands
    if (!allowDangerous) {
      const lowerCommand = command.toLowerCase();
      
      for (const dangerous of this.dangerousCommands) {
        if (lowerCommand.includes(dangerous.toLowerCase())) {
          throw new ToolError(
            `Potentially dangerous command detected: "${dangerous}". ` +
            'Use allowDangerous: true to execute this command.'
          );
        }
      }
    }
    
    // Additional security checks
    if (command.includes('$(') || command.includes('`')) {
      this.logger.warn('Command contains command substitution');
    }
    
    if (command.includes('&&') || command.includes('||') || command.includes(';')) {
      this.logger.warn('Command contains command chaining');
    }
  }

  async executeNonInteractive(command, args, options) {
    const { cwd, env, timeout, shell, captureOutput } = options;
    
    return new Promise((resolve, reject) => {
      const commandParts = this.parseCommand(command);
      const cmd = commandParts[0];
      const cmdArgs = [...commandParts.slice(1), ...args];
      
      const spawnOptions = {
        cwd,
        env,
        shell: shell || this.getDefaultShell(),
        stdio: captureOutput ? 'pipe' : 'inherit'
      };
      
      const child = spawn(cmd, cmdArgs, spawnOptions);
      
      let stdout = '';
      let stderr = '';
      
      if (captureOutput) {
        child.stdout?.on('data', (data) => {
          stdout += data.toString();
        });
        
        child.stderr?.on('data', (data) => {
          stderr += data.toString();
        });
      }
      
      const timeoutId = setTimeout(() => {
        child.kill('SIGTERM');
        reject(new ToolError(`Command timed out after ${timeout}ms`));
      }, timeout);
      
      child.on('close', (code, signal) => {
        clearTimeout(timeoutId);
        
        const result = {
          exitCode: code,
          signal,
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          success: code === 0
        };
        
        if (code === 0) {
          resolve(result);
        } else {
          reject(new ToolError(`Command failed with exit code ${code}: ${stderr || stdout}`));
        }
      });
      
      child.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(new ToolError(`Failed to execute command: ${error.message}`));
      });
    });
  }

  async executeInteractive(command, args, options) {
    const { cwd, env, shell } = options;
    
    return new Promise((resolve, reject) => {
      const commandParts = this.parseCommand(command);
      const cmd = commandParts[0];
      const cmdArgs = [...commandParts.slice(1), ...args];
      
      const spawnOptions = {
        cwd,
        env,
        shell: shell || this.getDefaultShell(),
        stdio: 'inherit'
      };
      
      const child = spawn(cmd, cmdArgs, spawnOptions);
      
      child.on('close', (code, signal) => {
        const result = {
          exitCode: code,
          signal,
          success: code === 0,
          interactive: true
        };
        
        if (code === 0) {
          resolve(result);
        } else {
          reject(new ToolError(`Interactive command failed with exit code ${code}`));
        }
      });
      
      child.on('error', (error) => {
        reject(new ToolError(`Failed to execute interactive command: ${error.message}`));
      });
    });
  }

  parseCommand(command) {
    // Simple command parsing - can be enhanced for complex cases
    return command.trim().split(/\s+/);
  }

  getDefaultShell() {
    const platform = os.platform();
    
    switch (platform) {
      case 'win32':
        return process.env.COMSPEC || 'cmd.exe';
      case 'darwin':
      case 'linux':
      default:
        return process.env.SHELL || '/bin/bash';
    }
  }

  // Utility methods for common operations
  async checkCommandExists(command) {
    try {
      await which(command);
      return true;
    } catch (error) {
      return false;
    }
  }

  async getSystemInfo() {
    const platform = os.platform();
    const arch = os.arch();
    const release = os.release();
    const hostname = os.hostname();
    
    let additionalInfo = {};
    
    try {
      if (platform === 'linux') {
        const result = await execAsync('lsb_release -a 2>/dev/null || cat /etc/os-release');
        additionalInfo.distribution = result.stdout;
      } else if (platform === 'darwin') {
        const result = await execAsync('sw_vers');
        additionalInfo.macosVersion = result.stdout;
      } else if (platform === 'win32') {
        const result = await execAsync('ver');
        additionalInfo.windowsVersion = result.stdout;
      }
    } catch (error) {
      this.logger.warn('Failed to get additional system info:', error);
    }
    
    return {
      platform,
      arch,
      release,
      hostname,
      ...additionalInfo
    };
  }

  async listRunningProcesses() {
    const platform = os.platform();
    let command;
    
    switch (platform) {
      case 'win32':
        command = 'tasklist /fo csv';
        break;
      case 'darwin':
      case 'linux':
      default:
        command = 'ps aux';
        break;
    }
    
    try {
      const result = await execAsync(command);
      return {
        command,
        output: result.stdout,
        processes: this.parseProcessList(result.stdout, platform)
      };
    } catch (error) {
      throw new ToolError(`Failed to list processes: ${error.message}`);
    }
  }

  parseProcessList(output, platform) {
    // Basic process parsing - can be enhanced
    const lines = output.split('\n').filter(line => line.trim());
    
    if (platform === 'win32') {
      // Parse CSV format from tasklist
      return lines.slice(1).map(line => {
        const parts = line.split(',').map(part => part.replace(/"/g, ''));
        return {
          name: parts[0],
          pid: parts[1],
          sessionName: parts[2],
          sessionNumber: parts[3],
          memUsage: parts[4]
        };
      });
    } else {
      // Parse ps aux format
      return lines.slice(1).map(line => {
        const parts = line.trim().split(/\s+/);
        return {
          user: parts[0],
          pid: parts[1],
          cpu: parts[2],
          mem: parts[3],
          command: parts.slice(10).join(' ')
        };
      });
    }
  }

  async killProcess(pid, signal = 'SIGTERM') {
    const platform = os.platform();
    let command;
    
    if (platform === 'win32') {
      command = `taskkill /PID ${pid} /F`;
    } else {
      command = `kill -${signal} ${pid}`;
    }
    
    try {
      const result = await execAsync(command);
      return {
        success: true,
        pid,
        signal,
        output: result.stdout
      };
    } catch (error) {
      throw new ToolError(`Failed to kill process ${pid}: ${error.message}`);
    }
  }

  async getEnvironmentVariables() {
    return {
      all: process.env,
      path: process.env.PATH?.split(path.delimiter) || [],
      important: {
        HOME: process.env.HOME,
        USER: process.env.USER,
        SHELL: process.env.SHELL,
        NODE_ENV: process.env.NODE_ENV,
        PWD: process.env.PWD
      }
    };
  }

  updateExecutionStats(executionTime) {
    if (!this.averageExecutionTime) {
      this.averageExecutionTime = executionTime;
    } else {
      this.averageExecutionTime = (this.averageExecutionTime + executionTime) / 2;
    }
  }

  async healthCheck() {
    try {
      // Test basic command execution
      const platform = os.platform();
      const testCommand = platform === 'win32' ? 'echo test' : 'echo test';
      
      const result = await this.executeNonInteractive(testCommand, [], {
        cwd: process.cwd(),
        env: process.env,
        timeout: 5000,
        captureOutput: true
      });
      
      return result.success && result.stdout.includes('test');
    } catch (error) {
      this.logger.error('Shell commands health check failed:', error);
      return false;
    }
  }
}

module.exports = ShellCommands;
