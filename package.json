{"name": "ai-cli-agent", "version": "1.0.0", "description": "Autonomous AI-Powered CLI Tool System with multi-LLM support and intelligent context management", "main": "src/cli.js", "bin": {"ai-cli": "./bin/ai-cli"}, "scripts": {"start": "node src/cli.js", "dev": "nodemon src/cli.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "pkg . --out-path dist/", "install-global": "npm link"}, "keywords": ["ai", "cli", "agent", "autonomous", "llm", "automation", "assistant"], "author": "AI CLI Agent", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@google/generative-ai": "^0.15.0", "axios": "^1.9.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "commander": "^11.1.0", "cross-spawn": "^7.0.3", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "glob": "^10.3.10", "inquirer": "^8.2.6", "jsonschema": "^1.4.1", "lodash": "^4.17.21", "mime-types": "^2.1.35", "node-cron": "^3.0.3", "ollama": "^0.5.0", "openai": "^4.20.1", "ora": "^5.4.1", "os-utils": "^0.0.14", "p-limit": "^3.1.0", "p-queue": "^6.6.2", "semver": "^7.5.4", "systeminformation": "^5.27.1", "tail": "^2.2.6", "which": "^4.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "yaml": "^2.3.4"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "pkg": "^5.8.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/ai-cli-agent/ai-cli-agent.git"}, "bugs": {"url": "https://github.com/ai-cli-agent/ai-cli-agent/issues"}, "homepage": "https://github.com/ai-cli-agent/ai-cli-agent#readme"}