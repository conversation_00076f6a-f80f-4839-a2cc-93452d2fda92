const EventEmitter = require('events');
const pLimit = require('p-limit');
const Logger = require('../utils/logger');
const { ExecutionError } = require('../utils/error-handler');
const ToolRegistry = require('../tools');

class ExecutionEngine extends EventEmitter {
  constructor(config) {
    super();
    this.config = config;
    this.logger = new Logger('ExecutionEngine');
    this.toolRegistry = new ToolRegistry();
    
    // Execution state
    this.activeExecutions = new Map();
    this.executionHistory = [];
    this.isInitialized = false;
    
    // Concurrency control
    this.maxConcurrency = config.maxConcurrency || 3;
    this.timeout = config.timeout || 300000; // 5 minutes
  }

  async initialize() {
    try {
      this.logger.info('Initializing Execution Engine...');

      // Only initialize tool registry if it hasn't been initialized yet
      if (!this.toolRegistry.isInitialized) {
        await this.toolRegistry.initialize();
      }

      this.isInitialized = true;
      this.logger.info('Execution Engine initialized');

    } catch (error) {
      this.logger.error('Failed to initialize execution engine:', error);
      throw new ExecutionError('Execution engine initialization failed', error);
    }
  }

  async execute(plan, options = {}) {
    if (!this.isInitialized) {
      throw new ExecutionError('Execution engine not initialized');
    }

    const executionId = this.generateExecutionId();
    
    try {
      this.logger.info(`Starting execution: ${executionId}`);
      
      const execution = {
        id: executionId,
        plan,
        options,
        startTime: Date.now(),
        status: 'running',
        completedSteps: 0,
        totalSteps: plan.steps.length,
        results: [],
        errors: []
      };

      this.activeExecutions.set(executionId, execution);
      this.emit('executionStarted', execution);

      // Validate plan
      this.validatePlan(plan);

      // Build dependency graph
      const dependencyGraph = this.buildDependencyGraph(plan.steps);

      // Execute steps based on dependencies and parallel options
      const result = await this.executeSteps(execution, dependencyGraph, options);

      // Finalize execution
      execution.endTime = Date.now();
      execution.duration = execution.endTime - execution.startTime;
      execution.status = result.success ? 'completed' : 'failed';

      this.activeExecutions.delete(executionId);
      this.executionHistory.push(execution);

      this.emit('executionCompleted', execution);
      this.logger.info(`Execution completed: ${executionId}`);

      return result;

    } catch (error) {
      this.logger.error(`Execution failed: ${executionId}`, error);
      
      const execution = this.activeExecutions.get(executionId);
      if (execution) {
        execution.status = 'failed';
        execution.error = error.message;
        this.activeExecutions.delete(executionId);
      }

      this.emit('executionFailed', { executionId, error });
      throw new ExecutionError('Plan execution failed', error);
    }
  }

  validatePlan(plan) {
    if (!plan || !plan.steps || !Array.isArray(plan.steps)) {
      throw new ExecutionError('Invalid plan: missing or invalid steps array');
    }

    if (plan.steps.length === 0) {
      throw new ExecutionError('Invalid plan: no steps to execute');
    }

    // Validate each step
    for (let i = 0; i < plan.steps.length; i++) {
      const step = plan.steps[i];
      
      if (!step.action || !step.tool) {
        throw new ExecutionError(`Invalid step ${i}: missing action or tool`);
      }

      if (!this.toolRegistry.hasTool(step.tool)) {
        throw new ExecutionError(`Invalid step ${i}: unknown tool '${step.tool}'`);
      }

      // Validate dependencies
      if (step.dependencies) {
        for (const dep of step.dependencies) {
          if (dep >= i) {
            throw new ExecutionError(`Invalid step ${i}: dependency ${dep} must be a previous step`);
          }
        }
      }
    }
  }

  buildDependencyGraph(steps) {
    const graph = new Map();
    
    steps.forEach((step, index) => {
      graph.set(index, {
        step,
        dependencies: step.dependencies || [],
        dependents: [],
        completed: false,
        running: false,
        result: null,
        error: null
      });
    });

    // Build reverse dependencies (dependents)
    graph.forEach((node, index) => {
      node.dependencies.forEach(depIndex => {
        const depNode = graph.get(depIndex);
        if (depNode) {
          depNode.dependents.push(index);
        }
      });
    });

    return graph;
  }

  async executeSteps(execution, dependencyGraph, options) {
    const { parallel = false, dryRun = false, maxConcurrency = 3 } = options;
    
    const limit = pLimit(maxConcurrency);
    const results = [];
    const errors = [];

    try {
      if (parallel) {
        await this.executeStepsParallel(execution, dependencyGraph, limit, dryRun);
      } else {
        await this.executeStepsSequential(execution, dependencyGraph, dryRun);
      }

      // Collect results
      dependencyGraph.forEach((node, index) => {
        if (node.result) {
          results.push({
            stepIndex: index,
            result: node.result
          });
        }
        if (node.error) {
          errors.push({
            stepIndex: index,
            error: node.error
          });
        }
      });

      const success = errors.length === 0;
      const summary = this.generateExecutionSummary(execution, results, errors);

      return {
        success,
        summary,
        results,
        errors,
        completedSteps: execution.completedSteps,
        totalSteps: execution.totalSteps,
        duration: execution.duration
      };

    } catch (error) {
      throw new ExecutionError('Step execution failed', error);
    }
  }

  async executeStepsSequential(execution, dependencyGraph, dryRun) {
    const completed = new Set();
    
    while (completed.size < dependencyGraph.size) {
      let progress = false;
      
      for (const [index, node] of dependencyGraph) {
        if (completed.has(index) || node.running) continue;
        
        // Check if all dependencies are completed
        const canExecute = node.dependencies.every(dep => completed.has(dep));
        
        if (canExecute) {
          progress = true;
          node.running = true;
          
          try {
            this.logger.debug(`Executing step ${index}: ${node.step.description}`);
            this.emit('stepStarted', { executionId: execution.id, stepIndex: index, step: node.step });
            
            if (!dryRun) {
              node.result = await this.executeStep(node.step);
            } else {
              node.result = { dryRun: true, step: node.step.description };
            }
            
            node.completed = true;
            completed.add(index);
            execution.completedSteps++;
            
            this.emit('stepCompleted', { 
              executionId: execution.id, 
              stepIndex: index, 
              result: node.result 
            });
            
          } catch (error) {
            node.error = error;
            this.logger.error(`Step ${index} failed:`, error);
            
            this.emit('stepFailed', { 
              executionId: execution.id, 
              stepIndex: index, 
              error 
            });
            
            // Decide whether to continue or stop
            if (!execution.options.continueOnError) {
              throw error;
            }
          }
          
          node.running = false;
        }
      }
      
      if (!progress) {
        throw new ExecutionError('Execution deadlock: no steps can be executed');
      }
    }
  }

  async executeStepsParallel(execution, dependencyGraph, limit, dryRun) {
    const completed = new Set();
    const running = new Set();
    
    const executeWhenReady = async (index, node) => {
      return limit(async () => {
        // Wait for dependencies
        while (!node.dependencies.every(dep => completed.has(dep))) {
          await this.delay(100);
        }
        
        running.add(index);
        node.running = true;
        
        try {
          this.logger.debug(`Executing step ${index}: ${node.step.description}`);
          this.emit('stepStarted', { executionId: execution.id, stepIndex: index, step: node.step });
          
          if (!dryRun) {
            node.result = await this.executeStep(node.step);
          } else {
            node.result = { dryRun: true, step: node.step.description };
          }
          
          node.completed = true;
          completed.add(index);
          running.delete(index);
          execution.completedSteps++;
          
          this.emit('stepCompleted', { 
            executionId: execution.id, 
            stepIndex: index, 
            result: node.result 
          });
          
        } catch (error) {
          node.error = error;
          running.delete(index);
          
          this.logger.error(`Step ${index} failed:`, error);
          this.emit('stepFailed', { 
            executionId: execution.id, 
            stepIndex: index, 
            error 
          });
          
          if (!execution.options.continueOnError) {
            throw error;
          }
        }
        
        node.running = false;
      });
    };

    // Start all steps that can run in parallel
    const promises = [];
    dependencyGraph.forEach((node, index) => {
      if (node.step.parallel !== false) {
        promises.push(executeWhenReady(index, node));
      }
    });

    await Promise.all(promises);
  }

  async executeStep(step) {
    const tool = this.toolRegistry.getTool(step.tool);
    if (!tool) {
      throw new ExecutionError(`Tool not found: ${step.tool}`);
    }

    try {
      const result = await tool.execute(step.parameters || {});
      
      return {
        success: true,
        output: result,
        tool: step.tool,
        action: step.action,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      throw new ExecutionError(`Tool execution failed: ${step.tool}`, error);
    }
  }

  generateExecutionSummary(execution, results, errors) {
    const { completedSteps, totalSteps } = execution;
    const successRate = (completedSteps / totalSteps * 100).toFixed(1);
    
    let summary = `Execution completed: ${completedSteps}/${totalSteps} steps (${successRate}% success rate)`;
    
    if (errors.length > 0) {
      summary += `\nErrors encountered: ${errors.length}`;
    }
    
    if (results.length > 0) {
      summary += `\nSuccessful operations: ${results.length}`;
    }
    
    return summary;
  }

  generateExecutionId() {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getActiveExecutions() {
    return Array.from(this.activeExecutions.values());
  }

  getExecutionHistory() {
    return this.executionHistory.slice(-50); // Return last 50 executions
  }

  async cancelExecution(executionId) {
    const execution = this.activeExecutions.get(executionId);
    if (execution) {
      execution.status = 'cancelled';
      this.activeExecutions.delete(executionId);
      this.emit('executionCancelled', execution);
      return true;
    }
    return false;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async healthCheck() {
    return this.isInitialized && this.toolRegistry.healthCheck();
  }

  getStatus() {
    return {
      initialized: this.isInitialized,
      activeExecutions: this.activeExecutions.size,
      totalExecutions: this.executionHistory.length,
      maxConcurrency: this.maxConcurrency,
      timeout: this.timeout,
      lastExecution: this.executionHistory.length > 0
        ? this.executionHistory[this.executionHistory.length - 1].id
        : null
    };
  }
}

module.exports = ExecutionEngine;
