const fs = require('fs-extra');
const path = require('path');
const { glob } = require('glob');
const mime = require('mime-types');
const Logger = require('../utils/logger');
const { ToolError } = require('../utils/error-handler');

class FileOperations {
  constructor() {
    this.name = 'file-operations';
    this.description = 'Comprehensive file system operations including read, write, create, delete, move, copy, and permissions';
    this.category = 'filesystem';
    this.logger = new Logger('FileOperations');
    this.executionCount = 0;
    this.lastUsed = null;
    
    this.schema = {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          enum: ['read', 'write', 'create', 'delete', 'move', 'copy', 'mkdir', 'rmdir', 'list', 'search', 'permissions', 'stats'],
          description: 'The file operation to perform'
        },
        path: {
          type: 'string',
          description: 'File or directory path'
        },
        content: {
          type: 'string',
          description: 'Content to write (for write/create operations)'
        },
        destination: {
          type: 'string',
          description: 'Destination path (for move/copy operations)'
        },
        pattern: {
          type: 'string',
          description: 'Search pattern (for search operations)'
        },
        recursive: {
          type: 'boolean',
          description: 'Whether to perform operation recursively',
          default: false
        },
        encoding: {
          type: 'string',
          description: 'File encoding',
          default: 'utf8'
        },
        mode: {
          type: 'string',
          description: 'File permissions mode (for permissions operation)'
        }
      },
      required: ['operation', 'path']
    };

    this.examples = [
      {
        description: 'Read a file',
        parameters: { operation: 'read', path: './README.md' }
      },
      {
        description: 'Write content to a file',
        parameters: { operation: 'write', path: './output.txt', content: 'Hello World!' }
      },
      {
        description: 'Search for files',
        parameters: { operation: 'search', path: './', pattern: '**/*.js' }
      }
    ];

    this.keywords = ['file', 'read', 'write', 'create', 'delete', 'copy', 'move', 'directory', 'folder'];
  }

  async initialize() {
    try {
      this.logger.debug('Initializing FileOperations tool...');

      // Simple initialization - don't perform file operations during init
      this.logger.debug('FileOperations tool initialized successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize FileOperations tool:', error);
      throw error;
    }
  }

  async execute(parameters) {
    this.executionCount++;
    this.lastUsed = new Date().toISOString();
    
    const startTime = Date.now();
    
    try {
      const { operation, path: filePath } = parameters;
      
      this.logger.debug(`Executing file operation: ${operation} on ${filePath}`);
      
      let result;
      
      switch (operation) {
        case 'read':
          result = await this.readFile(parameters);
          break;
        case 'write':
          result = await this.writeFile(parameters);
          break;
        case 'create':
          result = await this.createFile(parameters);
          break;
        case 'delete':
          result = await this.deleteFile(parameters);
          break;
        case 'move':
          result = await this.moveFile(parameters);
          break;
        case 'copy':
          result = await this.copyFile(parameters);
          break;
        case 'mkdir':
          result = await this.createDirectory(parameters);
          break;
        case 'rmdir':
          result = await this.removeDirectory(parameters);
          break;
        case 'list':
          result = await this.listDirectory(parameters);
          break;
        case 'search':
          result = await this.searchFiles(parameters);
          break;
        case 'permissions':
          result = await this.changePermissions(parameters);
          break;
        case 'stats':
          result = await this.getFileStats(parameters);
          break;
        default:
          throw new ToolError(`Unknown file operation: ${operation}`);
      }
      
      const executionTime = Date.now() - startTime;
      this.updateExecutionStats(executionTime);
      
      return {
        success: true,
        operation,
        path: filePath,
        result,
        executionTime
      };
      
    } catch (error) {
      this.logger.error('File operation failed:', error);
      throw new ToolError(`File operation failed: ${error.message}`, error);
    }
  }

  async readFile(parameters) {
    const { path: filePath, encoding = 'utf8' } = parameters;
    
    // Security check
    this.validatePath(filePath);
    
    if (!await fs.pathExists(filePath)) {
      throw new ToolError(`File does not exist: ${filePath}`);
    }
    
    const stats = await fs.stat(filePath);
    if (!stats.isFile()) {
      throw new ToolError(`Path is not a file: ${filePath}`);
    }
    
    // Check if file is binary
    const mimeType = mime.lookup(filePath);
    const isBinary = mimeType && !mimeType.startsWith('text/') && !mimeType.includes('json') && !mimeType.includes('xml');
    
    if (isBinary) {
      return {
        type: 'binary',
        mimeType,
        size: stats.size,
        message: 'Binary file detected. Use appropriate tools to handle binary content.'
      };
    }
    
    const content = await fs.readFile(filePath, encoding);
    
    return {
      type: 'text',
      content,
      size: stats.size,
      mimeType,
      encoding
    };
  }

  async writeFile(parameters) {
    const { path: filePath, content, encoding = 'utf8' } = parameters;
    
    this.validatePath(filePath);
    
    // Ensure directory exists
    const dir = path.dirname(filePath);
    await fs.ensureDir(dir);
    
    await fs.writeFile(filePath, content, encoding);
    
    const stats = await fs.stat(filePath);
    
    return {
      message: `File written successfully: ${filePath}`,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime
    };
  }

  async createFile(parameters) {
    const { path: filePath, content = '', encoding = 'utf8' } = parameters;
    
    this.validatePath(filePath);
    
    if (await fs.pathExists(filePath)) {
      throw new ToolError(`File already exists: ${filePath}`);
    }
    
    return await this.writeFile(parameters);
  }

  async deleteFile(parameters) {
    const { path: filePath } = parameters;
    
    this.validatePath(filePath);
    
    if (!await fs.pathExists(filePath)) {
      throw new ToolError(`File does not exist: ${filePath}`);
    }
    
    const stats = await fs.stat(filePath);
    await fs.remove(filePath);
    
    return {
      message: `File deleted successfully: ${filePath}`,
      wasDirectory: stats.isDirectory(),
      size: stats.size
    };
  }

  async moveFile(parameters) {
    const { path: sourcePath, destination } = parameters;
    
    this.validatePath(sourcePath);
    this.validatePath(destination);
    
    if (!await fs.pathExists(sourcePath)) {
      throw new ToolError(`Source file does not exist: ${sourcePath}`);
    }
    
    // Ensure destination directory exists
    const destDir = path.dirname(destination);
    await fs.ensureDir(destDir);
    
    await fs.move(sourcePath, destination);
    
    return {
      message: `File moved successfully: ${sourcePath} -> ${destination}`,
      source: sourcePath,
      destination
    };
  }

  async copyFile(parameters) {
    const { path: sourcePath, destination } = parameters;
    
    this.validatePath(sourcePath);
    this.validatePath(destination);
    
    if (!await fs.pathExists(sourcePath)) {
      throw new ToolError(`Source file does not exist: ${sourcePath}`);
    }
    
    // Ensure destination directory exists
    const destDir = path.dirname(destination);
    await fs.ensureDir(destDir);
    
    await fs.copy(sourcePath, destination);
    
    const stats = await fs.stat(destination);
    
    return {
      message: `File copied successfully: ${sourcePath} -> ${destination}`,
      source: sourcePath,
      destination,
      size: stats.size
    };
  }

  async createDirectory(parameters) {
    const { path: dirPath } = parameters;
    
    this.validatePath(dirPath);
    
    await fs.ensureDir(dirPath);
    
    return {
      message: `Directory created successfully: ${dirPath}`,
      path: dirPath
    };
  }

  async removeDirectory(parameters) {
    const { path: dirPath, recursive = false } = parameters;
    
    this.validatePath(dirPath);
    
    if (!await fs.pathExists(dirPath)) {
      throw new ToolError(`Directory does not exist: ${dirPath}`);
    }
    
    const stats = await fs.stat(dirPath);
    if (!stats.isDirectory()) {
      throw new ToolError(`Path is not a directory: ${dirPath}`);
    }
    
    if (recursive) {
      await fs.remove(dirPath);
    } else {
      await fs.rmdir(dirPath);
    }
    
    return {
      message: `Directory removed successfully: ${dirPath}`,
      recursive
    };
  }

  async listDirectory(parameters) {
    const { path: dirPath, recursive = false } = parameters;
    
    this.validatePath(dirPath);
    
    if (!await fs.pathExists(dirPath)) {
      throw new ToolError(`Directory does not exist: ${dirPath}`);
    }
    
    const stats = await fs.stat(dirPath);
    if (!stats.isDirectory()) {
      throw new ToolError(`Path is not a directory: ${dirPath}`);
    }
    
    let items;
    
    if (recursive) {
      const pattern = path.join(dirPath, '**/*');
      items = await glob(pattern, { dot: true });
    } else {
      const dirItems = await fs.readdir(dirPath);
      items = dirItems.map(item => path.join(dirPath, item));
    }
    
    const detailedItems = await Promise.all(
      items.map(async (item) => {
        try {
          const itemStats = await fs.stat(item);
          return {
            path: item,
            name: path.basename(item),
            type: itemStats.isDirectory() ? 'directory' : 'file',
            size: itemStats.size,
            modified: itemStats.mtime,
            permissions: itemStats.mode
          };
        } catch (error) {
          return {
            path: item,
            name: path.basename(item),
            type: 'unknown',
            error: error.message
          };
        }
      })
    );
    
    return {
      directory: dirPath,
      items: detailedItems,
      count: detailedItems.length,
      recursive
    };
  }

  async searchFiles(parameters) {
    const { path: searchPath, pattern, recursive = true } = parameters;
    
    this.validatePath(searchPath);
    
    const searchPattern = recursive 
      ? path.join(searchPath, '**', pattern)
      : path.join(searchPath, pattern);
    
    const matches = await glob(searchPattern, {
      dot: true,
      absolute: true
    });
    
    const results = await Promise.all(
      matches.map(async (match) => {
        try {
          const stats = await fs.stat(match);
          return {
            path: match,
            name: path.basename(match),
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime
          };
        } catch (error) {
          return {
            path: match,
            error: error.message
          };
        }
      })
    );
    
    return {
      pattern,
      searchPath,
      matches: results,
      count: results.length
    };
  }

  async changePermissions(parameters) {
    const { path: filePath, mode } = parameters;
    
    this.validatePath(filePath);
    
    if (!await fs.pathExists(filePath)) {
      throw new ToolError(`File does not exist: ${filePath}`);
    }
    
    await fs.chmod(filePath, mode);
    
    const stats = await fs.stat(filePath);
    
    return {
      message: `Permissions changed successfully: ${filePath}`,
      path: filePath,
      mode: stats.mode.toString(8),
      newMode: mode
    };
  }

  async getFileStats(parameters) {
    const { path: filePath } = parameters;
    
    this.validatePath(filePath);
    
    if (!await fs.pathExists(filePath)) {
      throw new ToolError(`File does not exist: ${filePath}`);
    }
    
    const stats = await fs.stat(filePath);
    
    return {
      path: filePath,
      type: stats.isDirectory() ? 'directory' : 'file',
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      accessed: stats.atime,
      permissions: stats.mode.toString(8),
      uid: stats.uid,
      gid: stats.gid
    };
  }

  validatePath(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      throw new ToolError('Invalid file path');
    }
    
    // Basic security checks
    const normalizedPath = path.normalize(filePath);
    
    // Prevent directory traversal attacks
    if (normalizedPath.includes('..')) {
      throw new ToolError('Directory traversal not allowed');
    }
    
    // Prevent access to sensitive system files
    const sensitivePatterns = [
      '/etc/passwd',
      '/etc/shadow',
      '/etc/hosts',
      'C:\\Windows\\System32',
      'C:\\Windows\\SysWOW64'
    ];
    
    for (const pattern of sensitivePatterns) {
      if (normalizedPath.toLowerCase().includes(pattern.toLowerCase())) {
        throw new ToolError('Access to sensitive system files not allowed');
      }
    }
  }

  updateExecutionStats(executionTime) {
    if (!this.averageExecutionTime) {
      this.averageExecutionTime = executionTime;
    } else {
      this.averageExecutionTime = (this.averageExecutionTime + executionTime) / 2;
    }
  }

  async healthCheck() {
    try {
      // Test basic file operations
      const testPath = path.join(process.cwd(), '.ai-cli-test');
      await fs.writeFile(testPath, 'test');
      await fs.readFile(testPath);
      await fs.remove(testPath);
      return true;
    } catch (error) {
      this.logger.error('File operations health check failed:', error);
      return false;
    }
  }
}

module.exports = FileOperations;
