const axios = require('axios');
const { spawn } = require('child_process');
const os = require('os');
const Logger = require('../utils/logger');
const { ToolError } = require('../utils/error-handler');

class NetworkOperations {
  constructor() {
    this.name = 'network-operations';
    this.description = 'Network operations including HTTP requests, connectivity tests, and network monitoring';
    this.category = 'network';
    this.logger = new Logger('NetworkOperations');
    
    // Performance tracking
    this.executionCount = 0;
    this.lastUsed = null;
    this.averageExecutionTime = 0;

    this.schema = {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          enum: ['http_request', 'ping', 'port_scan', 'dns_lookup', 'trace_route', 'network_info', 'speed_test'],
          description: 'Type of network operation to perform'
        },
        url: {
          type: 'string',
          description: 'URL for HTTP requests'
        },
        method: {
          type: 'string',
          enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD'],
          description: 'HTTP method for requests'
        },
        headers: {
          type: 'object',
          description: 'HTTP headers for requests'
        },
        data: {
          type: 'object',
          description: 'Request body data'
        },
        host: {
          type: 'string',
          description: 'Host for ping, port scan, or DNS lookup'
        },
        port: {
          type: 'number',
          description: 'Port number for port scanning'
        },
        timeout: {
          type: 'number',
          description: 'Timeout in milliseconds',
          default: 5000
        },
        retries: {
          type: 'number',
          description: 'Number of retries for failed operations',
          default: 3
        }
      },
      required: ['operation']
    };

    this.examples = [
      {
        description: 'Make HTTP GET request',
        parameters: { operation: 'http_request', url: 'https://api.github.com/user', method: 'GET' }
      },
      {
        description: 'Ping a host',
        parameters: { operation: 'ping', host: 'google.com' }
      },
      {
        description: 'Check if port is open',
        parameters: { operation: 'port_scan', host: 'localhost', port: 3000 }
      }
    ];

    this.keywords = ['network', 'http', 'request', 'ping', 'connectivity', 'api', 'web', 'internet'];
  }

  async initialize() {
    try {
      this.logger.debug('Initializing NetworkOperations tool...');

      // Test basic network access
      const testAccess = await this.healthCheck();
      if (!testAccess) {
        throw new Error('Network access test failed');
      }

      this.logger.debug('NetworkOperations tool initialized successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize NetworkOperations tool:', error);
      throw error;
    }
  }

  async execute(parameters) {
    this.executionCount++;
    this.lastUsed = new Date().toISOString();
    
    const startTime = Date.now();
    
    try {
      const { operation } = parameters;
      let result;
      
      switch (operation) {
        case 'http_request':
          result = await this.httpRequest(parameters);
          break;
        case 'ping':
          result = await this.ping(parameters);
          break;
        case 'port_scan':
          result = await this.portScan(parameters);
          break;
        case 'dns_lookup':
          result = await this.dnsLookup(parameters);
          break;
        case 'trace_route':
          result = await this.traceRoute(parameters);
          break;
        case 'network_info':
          result = await this.getNetworkInfo(parameters);
          break;
        case 'speed_test':
          result = await this.speedTest(parameters);
          break;
        default:
          throw new ToolError(`Unknown network operation: ${operation}`);
      }
      
      const executionTime = Date.now() - startTime;
      this.updateExecutionStats(executionTime);
      
      return {
        success: true,
        operation,
        result,
        executionTime,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      this.logger.error('Network operation failed:', error);
      throw new ToolError(`Network operation failed: ${error.message}`, error);
    }
  }

  async httpRequest(parameters) {
    const {
      url,
      method = 'GET',
      headers = {},
      data,
      timeout = 5000,
      retries = 3
    } = parameters;

    if (!url) {
      throw new ToolError('URL is required for HTTP requests');
    }

    const config = {
      method,
      url,
      headers,
      timeout,
      validateStatus: () => true // Don't throw on HTTP error status
    };

    if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      config.data = data;
    }

    let lastError;
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await axios(config);
        
        return {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data,
          responseTime: response.config.metadata?.endTime - response.config.metadata?.startTime || 0,
          attempt
        };
      } catch (error) {
        lastError = error;
        this.logger.warn(`HTTP request attempt ${attempt} failed:`, error.message);
        
        if (attempt < retries) {
          await this.delay(1000 * attempt); // Exponential backoff
        }
      }
    }

    throw new ToolError(`HTTP request failed after ${retries} attempts: ${lastError.message}`);
  }

  async ping(parameters) {
    const { host, timeout = 5000 } = parameters;

    if (!host) {
      throw new ToolError('Host is required for ping operation');
    }

    return new Promise((resolve, reject) => {
      const isWindows = os.platform() === 'win32';
      const pingCmd = isWindows ? 'ping' : 'ping';
      const args = isWindows 
        ? ['-n', '4', host]
        : ['-c', '4', host];

      const child = spawn(pingCmd, args, { stdio: 'pipe' });
      
      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      const timeoutId = setTimeout(() => {
        child.kill();
        reject(new ToolError(`Ping operation timed out after ${timeout}ms`));
      }, timeout);

      child.on('close', (code) => {
        clearTimeout(timeoutId);
        
        if (code === 0) {
          const result = this.parsePingOutput(stdout, isWindows);
          resolve({
            host,
            success: true,
            ...result
          });
        } else {
          reject(new ToolError(`Ping failed: ${stderr || stdout}`));
        }
      });

      child.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(new ToolError(`Ping command failed: ${error.message}`));
      });
    });
  }

  parsePingOutput(output, isWindows) {
    const lines = output.split('\n');
    const stats = {
      packetsSent: 0,
      packetsReceived: 0,
      packetLoss: 0,
      avgTime: 0,
      minTime: 0,
      maxTime: 0
    };

    if (isWindows) {
      // Parse Windows ping output
      const statsLine = lines.find(line => line.includes('Packets:'));
      if (statsLine) {
        const sent = statsLine.match(/Sent = (\d+)/);
        const received = statsLine.match(/Received = (\d+)/);
        const lost = statsLine.match(/Lost = (\d+)/);
        
        if (sent) stats.packetsSent = parseInt(sent[1]);
        if (received) stats.packetsReceived = parseInt(received[1]);
        if (lost) stats.packetLoss = (parseInt(lost[1]) / stats.packetsSent) * 100;
      }
    } else {
      // Parse Unix ping output
      const statsLine = lines.find(line => line.includes('packets transmitted'));
      if (statsLine) {
        const match = statsLine.match(/(\d+) packets transmitted, (\d+) received, (\d+)% packet loss/);
        if (match) {
          stats.packetsSent = parseInt(match[1]);
          stats.packetsReceived = parseInt(match[2]);
          stats.packetLoss = parseInt(match[3]);
        }
      }
      
      const timeLine = lines.find(line => line.includes('min/avg/max'));
      if (timeLine) {
        const match = timeLine.match(/= ([\d.]+)\/([\d.]+)\/([\d.]+)/);
        if (match) {
          stats.minTime = parseFloat(match[1]);
          stats.avgTime = parseFloat(match[2]);
          stats.maxTime = parseFloat(match[3]);
        }
      }
    }

    return stats;
  }

  async portScan(parameters) {
    const { host, port, timeout = 5000 } = parameters;

    if (!host || !port) {
      throw new ToolError('Host and port are required for port scanning');
    }

    return new Promise((resolve) => {
      const net = require('net');
      const socket = new net.Socket();
      
      const startTime = Date.now();
      
      socket.setTimeout(timeout);
      
      socket.on('connect', () => {
        const responseTime = Date.now() - startTime;
        socket.destroy();
        resolve({
          host,
          port,
          open: true,
          responseTime
        });
      });
      
      socket.on('timeout', () => {
        socket.destroy();
        resolve({
          host,
          port,
          open: false,
          error: 'Connection timeout'
        });
      });
      
      socket.on('error', (error) => {
        socket.destroy();
        resolve({
          host,
          port,
          open: false,
          error: error.code || error.message
        });
      });
      
      socket.connect(port, host);
    });
  }

  async dnsLookup(parameters) {
    const { host } = parameters;

    if (!host) {
      throw new ToolError('Host is required for DNS lookup');
    }

    const dns = require('dns').promises;
    
    try {
      const [ipv4, ipv6] = await Promise.allSettled([
        dns.resolve4(host),
        dns.resolve6(host)
      ]);

      return {
        host,
        ipv4: ipv4.status === 'fulfilled' ? ipv4.value : null,
        ipv6: ipv6.status === 'fulfilled' ? ipv6.value : null,
        success: ipv4.status === 'fulfilled' || ipv6.status === 'fulfilled'
      };
    } catch (error) {
      throw new ToolError(`DNS lookup failed: ${error.message}`);
    }
  }

  async getNetworkInfo() {
    const interfaces = os.networkInterfaces();
    const networkInfo = {};

    Object.keys(interfaces).forEach(name => {
      networkInfo[name] = interfaces[name].map(iface => ({
        address: iface.address,
        netmask: iface.netmask,
        family: iface.family,
        mac: iface.mac,
        internal: iface.internal,
        cidr: iface.cidr
      }));
    });

    return {
      interfaces: networkInfo,
      hostname: os.hostname(),
      platform: os.platform()
    };
  }

  async speedTest(parameters) {
    // Simple speed test using HTTP requests
    const testUrl = 'https://httpbin.org/bytes/1048576'; // 1MB test file
    const { iterations = 3 } = parameters;

    const results = [];
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      
      try {
        const response = await axios.get(testUrl, {
          timeout: 30000,
          responseType: 'arraybuffer'
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        const bytes = response.data.byteLength;
        const speedMbps = (bytes * 8) / (duration * 1000); // Mbps
        
        results.push({
          iteration: i + 1,
          bytes,
          duration,
          speedMbps: parseFloat(speedMbps.toFixed(2))
        });
      } catch (error) {
        results.push({
          iteration: i + 1,
          error: error.message
        });
      }
    }

    const successfulTests = results.filter(r => !r.error);
    const avgSpeed = successfulTests.length > 0 
      ? successfulTests.reduce((sum, r) => sum + r.speedMbps, 0) / successfulTests.length
      : 0;

    return {
      results,
      summary: {
        totalTests: iterations,
        successfulTests: successfulTests.length,
        averageSpeedMbps: parseFloat(avgSpeed.toFixed(2))
      }
    };
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  updateExecutionStats(executionTime) {
    if (!this.averageExecutionTime) {
      this.averageExecutionTime = executionTime;
    } else {
      this.averageExecutionTime = (this.averageExecutionTime + executionTime) / 2;
    }
  }

  async healthCheck() {
    try {
      // Simple health check - just verify the tool can be instantiated
      // Don't perform actual network operations during initialization
      return true;
    } catch (error) {
      this.logger.error('Network operations health check failed:', error);
      return false;
    }
  }
}

module.exports = NetworkOperations;
