const winston = require('winston');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');

class Logger {
  constructor(component = 'AI-CLI') {
    this.component = component;
    this.logDir = path.join(os.homedir(), '.ai-cli', 'logs');
    this.winston = null;
    this.setupLoggerSync();
  }

  setupLoggerSync() {
    try {
      // Ensure log directory exists synchronously
      fs.ensureDirSync(this.logDir);
      this.createWinstonLogger();
    } catch (error) {
      console.error('Failed to setup logger:', error.message);
      this.createFallbackLogger();
    }
  }

  createWinstonLogger() {
    // Define log format
    const logFormat = winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
      }),
      winston.format.errors({ stack: true }),
      winston.format.printf(({ timestamp, level, message, component, stack }) => {
        const comp = component || this.component;
        const logMessage = `${timestamp} [${level.toUpperCase()}] [${comp}] ${message}`;
        return stack ? `${logMessage}\n${stack}` : logMessage;
      })
    );

    // Create winston logger
    this.winston = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat,
      transports: [
        // Console transport
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            logFormat
          ),
          silent: process.env.NODE_ENV === 'test'
        }),
        
        // File transport for all logs
        new winston.transports.File({
          filename: path.join(this.logDir, 'ai-cli.log'),
          maxsize: 10485760, // 10MB
          maxFiles: 5,
          tailable: true
        }),
        
        // Separate file for errors
        new winston.transports.File({
          filename: path.join(this.logDir, 'error.log'),
          level: 'error',
          maxsize: 10485760, // 10MB
          maxFiles: 3,
          tailable: true
        })
      ],
      
      // Handle uncaught exceptions
      exceptionHandlers: [
        new winston.transports.File({
          filename: path.join(this.logDir, 'exceptions.log')
        })
      ],
      
      // Handle unhandled promise rejections
      rejectionHandlers: [
        new winston.transports.File({
          filename: path.join(this.logDir, 'rejections.log')
        })
      ]
    });

    // Add daily rotate file transport for production
    if (process.env.NODE_ENV === 'production') {
      try {
        const DailyRotateFile = require('winston-daily-rotate-file');

        this.winston.add(new DailyRotateFile({
          filename: path.join(this.logDir, 'ai-cli-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '14d'
        }));
      } catch (error) {
        // Daily rotate file not available, continue without it
      }
    }
  }

  createFallbackLogger() {
    // Simple console logger as fallback
    this.winston = {
      debug: (message, meta) => console.log(`[DEBUG] [${this.component}] ${message}`),
      info: (message, meta) => console.log(`[INFO] [${this.component}] ${message}`),
      warn: (message, meta) => console.warn(`[WARN] [${this.component}] ${message}`),
      error: (message, meta) => console.error(`[ERROR] [${this.component}] ${message}`),
      level: 'info'
    };
  }

  // Core logging methods
  debug(message, meta = {}) {
    this.winston.debug(message, { component: this.component, ...meta });
  }

  info(message, meta = {}) {
    this.winston.info(message, { component: this.component, ...meta });
  }

  warn(message, meta = {}) {
    this.winston.warn(message, { component: this.component, ...meta });
  }

  error(message, error = null, meta = {}) {
    const logData = { component: this.component, ...meta };
    
    if (error) {
      if (error instanceof Error) {
        logData.error = {
          name: error.name,
          message: error.message,
          stack: error.stack
        };
      } else {
        logData.error = error;
      }
    }
    
    this.winston.error(message, logData);
  }

  // Specialized logging methods
  logExecution(executionId, operation, status, details = {}) {
    this.info(`Execution ${status}`, {
      executionId,
      operation,
      status,
      ...details
    });
  }

  logToolUsage(toolName, operation, duration, success = true) {
    this.info(`Tool executed: ${toolName}`, {
      tool: toolName,
      operation,
      duration,
      success
    });
  }

  logLLMRequest(provider, model, tokens, duration) {
    this.debug(`LLM request completed`, {
      provider,
      model,
      tokens,
      duration,
      type: 'llm_request'
    });
  }

  logSecurityEvent(event, details = {}) {
    this.warn(`Security event: ${event}`, {
      event,
      type: 'security',
      ...details
    });
  }

  logPerformance(operation, duration, details = {}) {
    this.debug(`Performance: ${operation}`, {
      operation,
      duration,
      type: 'performance',
      ...details
    });
  }

  // Context-aware logging
  withContext(context) {
    return {
      debug: (message, meta = {}) => this.debug(message, { ...context, ...meta }),
      info: (message, meta = {}) => this.info(message, { ...context, ...meta }),
      warn: (message, meta = {}) => this.warn(message, { ...context, ...meta }),
      error: (message, error = null, meta = {}) => this.error(message, error, { ...context, ...meta })
    };
  }

  // Utility methods
  setLevel(level) {
    this.winston.level = level;
    this.winston.transports.forEach(transport => {
      if (transport.level !== 'error') {
        transport.level = level;
      }
    });
  }

  getLevel() {
    return this.winston.level;
  }

  // Log file management
  async getLogFiles() {
    try {
      const files = await fs.readdir(this.logDir);
      const logFiles = files.filter(file => file.endsWith('.log'));
      
      const fileDetails = await Promise.all(
        logFiles.map(async file => {
          const filePath = path.join(this.logDir, file);
          const stats = await fs.stat(filePath);
          return {
            name: file,
            path: filePath,
            size: stats.size,
            modified: stats.mtime
          };
        })
      );
      
      return fileDetails.sort((a, b) => b.modified - a.modified);
    } catch (error) {
      this.error('Failed to get log files', error);
      return [];
    }
  }

  async readLogFile(filename, lines = 100) {
    try {
      const filePath = path.join(this.logDir, filename);
      const content = await fs.readFile(filePath, 'utf8');
      const allLines = content.split('\n');
      
      return allLines.slice(-lines).join('\n');
    } catch (error) {
      this.error(`Failed to read log file: ${filename}`, error);
      throw error;
    }
  }

  async clearLogs() {
    try {
      const files = await fs.readdir(this.logDir);
      const logFiles = files.filter(file => file.endsWith('.log'));
      
      await Promise.all(
        logFiles.map(file => fs.remove(path.join(this.logDir, file)))
      );
      
      this.info('Log files cleared');
    } catch (error) {
      this.error('Failed to clear logs', error);
      throw error;
    }
  }

  async rotateLogs() {
    try {
      const files = await this.getLogFiles();
      const oldFiles = files.filter(file => {
        const age = Date.now() - file.modified.getTime();
        return age > 30 * 24 * 60 * 60 * 1000; // 30 days
      });
      
      await Promise.all(
        oldFiles.map(file => fs.remove(file.path))
      );
      
      this.info(`Rotated ${oldFiles.length} old log files`);
    } catch (error) {
      this.error('Failed to rotate logs', error);
    }
  }

  // Statistics and monitoring
  async getLogStats() {
    try {
      const files = await this.getLogFiles();
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      
      // Count log entries by level (simplified)
      const mainLogFile = path.join(this.logDir, 'ai-cli.log');
      let logCounts = { debug: 0, info: 0, warn: 0, error: 0 };
      
      if (await fs.pathExists(mainLogFile)) {
        const content = await fs.readFile(mainLogFile, 'utf8');
        const lines = content.split('\n');
        
        lines.forEach(line => {
          if (line.includes('[DEBUG]')) logCounts.debug++;
          else if (line.includes('[INFO]')) logCounts.info++;
          else if (line.includes('[WARN]')) logCounts.warn++;
          else if (line.includes('[ERROR]')) logCounts.error++;
        });
      }
      
      return {
        totalFiles: files.length,
        totalSize,
        logCounts,
        oldestLog: files.length > 0 ? files[files.length - 1].modified : null,
        newestLog: files.length > 0 ? files[0].modified : null
      };
    } catch (error) {
      this.error('Failed to get log statistics', error);
      return null;
    }
  }

  // Stream logs in real-time
  createLogStream() {
    const { Tail } = require('tail');
    const mainLogFile = path.join(this.logDir, 'ai-cli.log');
    
    try {
      const tail = new Tail(mainLogFile, {
        separator: '\n',
        fromBeginning: false,
        fsWatchOptions: {},
        follow: true
      });
      
      return tail;
    } catch (error) {
      this.error('Failed to create log stream', error);
      return null;
    }
  }

  // Export logs
  async exportLogs(outputPath, format = 'json') {
    try {
      const files = await this.getLogFiles();
      const logs = [];
      
      for (const file of files) {
        const content = await fs.readFile(file.path, 'utf8');
        const lines = content.split('\n').filter(line => line.trim());
        
        lines.forEach(line => {
          try {
            // Parse log line (simplified)
            const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\] \[([^\]]+)\] (.+)$/);
            if (match) {
              logs.push({
                timestamp: match[1],
                level: match[2],
                component: match[3],
                message: match[4],
                file: file.name
              });
            }
          } catch (parseError) {
            // Skip unparseable lines
          }
        });
      }
      
      let output;
      if (format === 'json') {
        output = JSON.stringify(logs, null, 2);
      } else if (format === 'csv') {
        const csv = logs.map(log => 
          `"${log.timestamp}","${log.level}","${log.component}","${log.message.replace(/"/g, '""')}","${log.file}"`
        ).join('\n');
        output = 'timestamp,level,component,message,file\n' + csv;
      } else {
        output = logs.map(log => 
          `${log.timestamp} [${log.level}] [${log.component}] ${log.message}`
        ).join('\n');
      }
      
      await fs.writeFile(outputPath, output, 'utf8');
      this.info(`Logs exported to: ${outputPath}`);
      
      return outputPath;
    } catch (error) {
      this.error('Failed to export logs', error);
      throw error;
    }
  }
}

module.exports = Logger;
