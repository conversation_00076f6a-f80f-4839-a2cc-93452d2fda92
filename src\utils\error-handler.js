const chalk = require('chalk');
const Logger = require('./logger');

// Custom error classes
class AICliError extends Error {
  constructor(message, originalError = null, code = null) {
    super(message);
    this.name = this.constructor.name;
    this.originalError = originalError;
    this.code = code;
    this.timestamp = new Date().toISOString();
    
    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      timestamp: this.timestamp,
      stack: this.stack,
      originalError: this.originalError ? {
        name: this.originalError.name,
        message: this.originalError.message,
        stack: this.originalError.stack
      } : null
    };
  }
}

class AgentError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError, 'AGENT_ERROR');
  }
}

class LLMError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError, 'LLM_ERROR');
  }
}

class ToolError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError, 'TOOL_ERROR');
  }
}

class ExecutionError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError, 'EXECUTION_ERROR');
  }
}

class ContextError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError, 'CONTEXT_ERROR');
  }
}

class ConfigError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError, 'CONFIG_ERROR');
  }
}

class SecurityError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError, 'SECURITY_ERROR');
  }
}

class ValidationError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError, 'VALIDATION_ERROR');
  }
}

// Error handler class
class ErrorHandler {
  constructor() {
    this.logger = new Logger('ErrorHandler');
    this.errorCounts = new Map();
    this.recentErrors = [];
    this.maxRecentErrors = 50;
  }

  handle(error, context = '', exitOnError = false) {
    // Increment error count
    const errorType = error.constructor.name;
    this.errorCounts.set(errorType, (this.errorCounts.get(errorType) || 0) + 1);
    
    // Add to recent errors
    this.recentErrors.unshift({
      error: error.toJSON ? error.toJSON() : {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context,
      timestamp: new Date().toISOString()
    });
    
    // Keep recent errors list manageable
    if (this.recentErrors.length > this.maxRecentErrors) {
      this.recentErrors = this.recentErrors.slice(0, this.maxRecentErrors);
    }
    
    // Log the error
    this.logger.error(`${context}: ${error.message}`, error);
    
    // Display user-friendly error message
    this.displayError(error, context);
    
    // Exit if requested
    if (exitOnError) {
      process.exit(1);
    }
  }

  displayError(error, context = '') {
    console.error(chalk.red('\n❌ Error occurred:'));
    
    if (context) {
      console.error(chalk.gray(`Context: ${context}`));
    }
    
    // Display error based on type
    if (error instanceof LLMError) {
      console.error(chalk.red('🤖 LLM Provider Error:'), error.message);
      this.suggestLLMSolution(error);
    } else if (error instanceof ToolError) {
      console.error(chalk.red('🔧 Tool Error:'), error.message);
      this.suggestToolSolution(error);
    } else if (error instanceof ConfigError) {
      console.error(chalk.red('⚙️ Configuration Error:'), error.message);
      this.suggestConfigSolution(error);
    } else if (error instanceof SecurityError) {
      console.error(chalk.red('🔒 Security Error:'), error.message);
      this.suggestSecuritySolution(error);
    } else if (error instanceof ValidationError) {
      console.error(chalk.red('✅ Validation Error:'), error.message);
      this.suggestValidationSolution(error);
    } else {
      console.error(chalk.red('💥 Unexpected Error:'), error.message);
    }
    
    // Show stack trace in debug mode
    if (process.env.DEBUG || process.env.LOG_LEVEL === 'debug') {
      console.error(chalk.gray('\nStack trace:'));
      console.error(chalk.gray(error.stack));
    }
    
    console.error(); // Empty line for spacing
  }

  suggestLLMSolution(error) {
    console.error(chalk.yellow('\n💡 Suggestions:'));
    
    if (error.message.includes('API key')) {
      console.error(chalk.yellow('  • Check your API key configuration'));
      console.error(chalk.yellow('  • Run: ai-cli config --setup'));
    } else if (error.message.includes('rate limit') || error.message.includes('quota')) {
      console.error(chalk.yellow('  • You may have exceeded API rate limits'));
      console.error(chalk.yellow('  • Try again in a few minutes'));
      console.error(chalk.yellow('  • Consider using a different provider'));
    } else if (error.message.includes('network') || error.message.includes('connection')) {
      console.error(chalk.yellow('  • Check your internet connection'));
      console.error(chalk.yellow('  • Verify the API endpoint is accessible'));
    } else {
      console.error(chalk.yellow('  • Try switching to a fallback provider'));
      console.error(chalk.yellow('  • Check provider status pages'));
    }
  }

  suggestToolSolution(error) {
    console.error(chalk.yellow('\n💡 Suggestions:'));
    
    if (error.message.includes('permission') || error.message.includes('access')) {
      console.error(chalk.yellow('  • Check file/directory permissions'));
      console.error(chalk.yellow('  • Ensure you have necessary access rights'));
    } else if (error.message.includes('not found') || error.message.includes('does not exist')) {
      console.error(chalk.yellow('  • Verify the file or command exists'));
      console.error(chalk.yellow('  • Check the path is correct'));
    } else if (error.message.includes('timeout')) {
      console.error(chalk.yellow('  • The operation took too long'));
      console.error(chalk.yellow('  • Try increasing timeout settings'));
    } else {
      console.error(chalk.yellow('  • Check tool configuration'));
      console.error(chalk.yellow('  • Verify required dependencies are installed'));
    }
  }

  suggestConfigSolution(error) {
    console.error(chalk.yellow('\n💡 Suggestions:'));
    console.error(chalk.yellow('  • Run the setup wizard: ai-cli config --setup'));
    console.error(chalk.yellow('  • Check configuration file syntax'));
    console.error(chalk.yellow('  • Reset to defaults: ai-cli config --reset'));
  }

  suggestSecuritySolution(error) {
    console.error(chalk.yellow('\n💡 Suggestions:'));
    console.error(chalk.yellow('  • Review security settings in configuration'));
    console.error(chalk.yellow('  • Use --allow-dangerous flag if operation is intentional'));
    console.error(chalk.yellow('  • Check file/directory permissions'));
  }

  suggestValidationSolution(error) {
    console.error(chalk.yellow('\n💡 Suggestions:'));
    console.error(chalk.yellow('  • Check command syntax and parameters'));
    console.error(chalk.yellow('  • Refer to help: ai-cli --help'));
    console.error(chalk.yellow('  • Verify input data format'));
  }

  // Recovery suggestions
  suggestRecovery(error) {
    const suggestions = [];
    
    if (error instanceof LLMError) {
      suggestions.push('Try switching to a different LLM provider');
      suggestions.push('Check API key and quota limits');
      suggestions.push('Verify network connectivity');
    }
    
    if (error instanceof ToolError) {
      suggestions.push('Check tool permissions and dependencies');
      suggestions.push('Verify file paths and existence');
      suggestions.push('Review tool configuration');
    }
    
    if (error instanceof ConfigError) {
      suggestions.push('Run configuration setup wizard');
      suggestions.push('Reset configuration to defaults');
      suggestions.push('Check configuration file syntax');
    }
    
    return suggestions;
  }

  // Error statistics
  getErrorStats() {
    return {
      totalErrors: this.recentErrors.length,
      errorCounts: Object.fromEntries(this.errorCounts),
      recentErrors: this.recentErrors.slice(0, 10), // Last 10 errors
      mostCommonError: this.getMostCommonError()
    };
  }

  getMostCommonError() {
    if (this.errorCounts.size === 0) return null;
    
    let maxCount = 0;
    let mostCommon = null;
    
    for (const [errorType, count] of this.errorCounts) {
      if (count > maxCount) {
        maxCount = count;
        mostCommon = errorType;
      }
    }
    
    return { type: mostCommon, count: maxCount };
  }

  // Clear error history
  clearHistory() {
    this.recentErrors = [];
    this.errorCounts.clear();
    this.logger.info('Error history cleared');
  }

  // Export error report
  generateErrorReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.getErrorStats(),
      recentErrors: this.recentErrors,
      systemInfo: {
        platform: process.platform,
        nodeVersion: process.version,
        arch: process.arch,
        uptime: process.uptime()
      }
    };
    
    return report;
  }

  // Check for error patterns
  detectErrorPatterns() {
    const patterns = [];
    
    // Check for repeated errors
    const errorMessages = this.recentErrors.map(e => e.error.message);
    const messageCounts = {};
    
    errorMessages.forEach(msg => {
      messageCounts[msg] = (messageCounts[msg] || 0) + 1;
    });
    
    for (const [message, count] of Object.entries(messageCounts)) {
      if (count >= 3) {
        patterns.push({
          type: 'repeated_error',
          message,
          count,
          suggestion: 'This error is occurring repeatedly. Consider investigating the root cause.'
        });
      }
    }
    
    // Check for error spikes
    const recentErrorsLast5Min = this.recentErrors.filter(e => {
      const errorTime = new Date(e.timestamp);
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      return errorTime > fiveMinutesAgo;
    });
    
    if (recentErrorsLast5Min.length >= 5) {
      patterns.push({
        type: 'error_spike',
        count: recentErrorsLast5Min.length,
        timeframe: '5 minutes',
        suggestion: 'High error rate detected. System may be experiencing issues.'
      });
    }
    
    return patterns;
  }
}

// Global error handler instance
const errorHandler = new ErrorHandler();

// Convenience function for handling errors
function handleError(error, context = '', exitOnError = false) {
  errorHandler.handle(error, context, exitOnError);
}

// Setup global error handlers
function setupGlobalErrorHandlers() {
  // Increase max listeners to prevent warnings
  process.setMaxListeners(20);

  // Only set up handlers if they haven't been set up already
  if (!process.listenerCount('uncaughtException')) {
    process.on('uncaughtException', (error) => {
      handleError(error, 'Uncaught Exception', true);
    });
  }

  if (!process.listenerCount('unhandledRejection')) {
    process.on('unhandledRejection', (reason, promise) => {
      const error = reason instanceof Error ? reason : new Error(String(reason));
      handleError(error, 'Unhandled Promise Rejection', false);
    });
  }
}

// Additional error classes
class PluginError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError);
    this.name = 'PluginError';
  }
}

class NetworkError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError);
    this.name = 'NetworkError';
  }
}

class MonitoringError extends AICliError {
  constructor(message, originalError = null) {
    super(message, originalError);
    this.name = 'MonitoringError';
  }
}

module.exports = {
  // Error classes
  AICliError,
  AgentError,
  LLMError,
  ToolError,
  ExecutionError,
  ContextError,
  ConfigError,
  SecurityError,
  ValidationError,
  PluginError,
  NetworkError,
  MonitoringError,

  // Error handler
  ErrorHandler,
  errorHandler,
  handleError,
  setupGlobalErrorHandlers
};
