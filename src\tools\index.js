const fs = require('fs-extra');
const path = require('path');
const Logger = require('../utils/logger');
const { ToolError } = require('../utils/error-handler');

// Import tool modules
const FileOperations = require('./file-operations');
const ShellCommands = require('./shell-commands');
const SystemInfo = require('./system-info');
const NetworkOperations = require('./network-operations');
const MonitoringAnalytics = require('./monitoring-analytics');

class ToolRegistry {
  constructor() {
    this.tools = new Map();
    this.logger = new Logger('ToolRegistry');
    this.isInitialized = false;
  }

  async initialize() {
    try {
      this.logger.info('Initializing Tool Registry...');
      
      // Register built-in tools
      await this.registerBuiltInTools();
      
      // Load custom tools if any
      await this.loadCustomTools();
      
      this.isInitialized = true;
      this.logger.info(`Tool Registry initialized with ${this.tools.size} tools`);
      
    } catch (error) {
      this.logger.error('Failed to initialize tool registry:', error);
      throw new ToolError('Tool registry initialization failed', error);
    }
  }

  async registerBuiltInTools() {
    const builtInTools = [
      new FileOperations(),
      new ShellCommands(),
      new SystemInfo(),
      new NetworkOperations(),
      new MonitoringAnalytics()
    ];

    for (const tool of builtInTools) {
      await this.registerTool(tool);
    }
  }

  async registerTool(tool) {
    try {
      // Validate tool interface
      this.validateTool(tool);
      
      // Initialize the tool
      if (typeof tool.initialize === 'function') {
        await tool.initialize();
      }
      
      this.tools.set(tool.name, tool);
      this.logger.debug(`Registered tool: ${tool.name}`);
      
    } catch (error) {
      this.logger.error(`Failed to register tool ${tool.name}:`, error);
      throw new ToolError(`Tool registration failed: ${tool.name}`, error);
    }
  }

  validateTool(tool) {
    if (!tool.name || typeof tool.name !== 'string') {
      throw new ToolError('Tool must have a valid name');
    }

    if (!tool.description || typeof tool.description !== 'string') {
      throw new ToolError('Tool must have a description');
    }

    if (typeof tool.execute !== 'function') {
      throw new ToolError('Tool must have an execute method');
    }

    if (!tool.schema || typeof tool.schema !== 'object') {
      throw new ToolError('Tool must have a parameter schema');
    }

    if (this.tools.has(tool.name)) {
      throw new ToolError(`Tool with name '${tool.name}' already exists`);
    }
  }

  async loadCustomTools() {
    const customToolsPath = path.join(process.cwd(), 'ai-cli-tools');
    
    if (await fs.pathExists(customToolsPath)) {
      try {
        const toolFiles = await fs.readdir(customToolsPath);
        
        for (const file of toolFiles) {
          if (file.endsWith('.js')) {
            const toolPath = path.join(customToolsPath, file);
            const ToolClass = require(toolPath);
            
            if (typeof ToolClass === 'function') {
              const tool = new ToolClass();
              await this.registerTool(tool);
            }
          }
        }
        
        this.logger.info(`Loaded ${toolFiles.length} custom tools`);
        
      } catch (error) {
        this.logger.warn('Failed to load custom tools:', error);
      }
    }
  }

  getTool(name) {
    return this.tools.get(name);
  }

  hasTool(name) {
    return this.tools.has(name);
  }

  getAvailableTools() {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      category: tool.category || 'general',
      schema: tool.schema,
      examples: tool.examples || []
    }));
  }

  getToolsByCategory(category) {
    return Array.from(this.tools.values()).filter(tool => 
      tool.category === category
    );
  }

  async executeTool(name, parameters = {}) {
    const tool = this.getTool(name);
    if (!tool) {
      throw new ToolError(`Tool not found: ${name}`);
    }

    try {
      // Validate parameters against schema
      this.validateParameters(tool, parameters);
      
      // Execute the tool
      const result = await tool.execute(parameters);
      
      this.logger.debug(`Tool executed successfully: ${name}`);
      return result;
      
    } catch (error) {
      this.logger.error(`Tool execution failed: ${name}`, error);
      throw new ToolError(`Tool execution failed: ${name}`, error);
    }
  }

  validateParameters(tool, parameters) {
    const schema = tool.schema;
    
    // Basic validation - can be enhanced with a proper schema validator
    if (schema.required) {
      for (const requiredParam of schema.required) {
        if (!(requiredParam in parameters)) {
          throw new ToolError(`Missing required parameter: ${requiredParam}`);
        }
      }
    }

    // Type validation
    if (schema.properties) {
      for (const [param, value] of Object.entries(parameters)) {
        const propSchema = schema.properties[param];
        if (propSchema && propSchema.type) {
          const actualType = typeof value;
          if (actualType !== propSchema.type) {
            throw new ToolError(`Parameter '${param}' must be of type ${propSchema.type}, got ${actualType}`);
          }
        }
      }
    }
  }

  async healthCheck() {
    try {
      let healthyTools = 0;
      
      for (const [name, tool] of this.tools) {
        try {
          if (typeof tool.healthCheck === 'function') {
            const isHealthy = await tool.healthCheck();
            if (isHealthy) healthyTools++;
          } else {
            healthyTools++; // Assume healthy if no health check
          }
        } catch (error) {
          this.logger.warn(`Health check failed for tool ${name}:`, error);
        }
      }
      
      const totalTools = this.tools.size;
      const healthRatio = totalTools > 0 ? healthyTools / totalTools : 1;
      
      this.logger.debug(`Tool health check: ${healthyTools}/${totalTools} tools healthy`);
      return healthRatio >= 0.8; // Consider healthy if 80% of tools are working
      
    } catch (error) {
      this.logger.error('Tool registry health check failed:', error);
      return false;
    }
  }

  getToolUsageStats() {
    const stats = {};
    
    for (const [name, tool] of this.tools) {
      stats[name] = {
        name: tool.name,
        category: tool.category || 'general',
        executionCount: tool.executionCount || 0,
        lastUsed: tool.lastUsed || null,
        averageExecutionTime: tool.averageExecutionTime || 0
      };
    }
    
    return stats;
  }

  // Tool discovery and suggestion
  suggestTools(intent, context = {}) {
    const suggestions = [];
    
    for (const tool of this.tools.values()) {
      let score = 0;
      
      // Match by name or description
      if (tool.name.toLowerCase().includes(intent.toLowerCase()) ||
          tool.description.toLowerCase().includes(intent.toLowerCase())) {
        score += 10;
      }
      
      // Match by keywords if available
      if (tool.keywords) {
        for (const keyword of tool.keywords) {
          if (intent.toLowerCase().includes(keyword.toLowerCase())) {
            score += 5;
          }
        }
      }
      
      // Context-based scoring
      if (context.projectType && tool.supportedProjectTypes) {
        if (tool.supportedProjectTypes.includes(context.projectType)) {
          score += 3;
        }
      }
      
      if (score > 0) {
        suggestions.push({
          tool: tool.name,
          score,
          reason: this.generateSuggestionReason(tool, intent, score)
        });
      }
    }
    
    return suggestions
      .sort((a, b) => b.score - a.score)
      .slice(0, 5); // Return top 5 suggestions
  }

  generateSuggestionReason(tool, intent, score) {
    if (score >= 10) {
      return `Directly matches your intent: "${intent}"`;
    } else if (score >= 5) {
      return `Related to your request and commonly used for similar tasks`;
    } else {
      return `Might be useful based on current context`;
    }
  }

  // Export tool definitions for external use
  exportToolDefinitions() {
    const definitions = {};
    
    for (const [name, tool] of this.tools) {
      definitions[name] = {
        name: tool.name,
        description: tool.description,
        category: tool.category || 'general',
        schema: tool.schema,
        examples: tool.examples || []
      };
    }
    
    return definitions;
  }

  // Import tool definitions
  async importToolDefinitions(definitions) {
    for (const [name, definition] of Object.entries(definitions)) {
      // Create a dynamic tool from definition
      const DynamicTool = class {
        constructor() {
          this.name = definition.name;
          this.description = definition.description;
          this.category = definition.category;
          this.schema = definition.schema;
          this.examples = definition.examples;
        }

        async execute(parameters) {
          throw new ToolError('Dynamic tool execution not implemented');
        }
      };

      await this.registerTool(new DynamicTool());
    }
  }

  async healthCheck() {
    const results = {};

    for (const [name, tool] of this.tools) {
      try {
        if (typeof tool.healthCheck === 'function') {
          results[name] = await tool.healthCheck();
        } else {
          results[name] = true; // Assume healthy if no health check method
        }
      } catch (error) {
        this.logger.error(`Health check failed for tool ${name}:`, error);
        results[name] = false;
      }
    }

    return results;
  }

  getToolUsageStats() {
    const stats = {};

    for (const [name, tool] of this.tools) {
      stats[name] = {
        executionCount: tool.executionCount || 0,
        lastUsed: tool.lastUsed || null,
        averageExecutionTime: tool.averageExecutionTime || 0,
        category: tool.category || 'unknown',
        description: tool.description || 'No description available'
      };
    }

    return stats;
  }

  getToolsByCategory() {
    const categories = {};

    for (const [name, tool] of this.tools) {
      const category = tool.category || 'uncategorized';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push({
        name,
        description: tool.description,
        keywords: tool.keywords || []
      });
    }

    return categories;
  }

  searchTools(query) {
    const results = [];
    const searchTerm = query.toLowerCase();

    for (const [name, tool] of this.tools) {
      let score = 0;

      // Check name match
      if (name.toLowerCase().includes(searchTerm)) {
        score += 10;
      }

      // Check description match
      if (tool.description && tool.description.toLowerCase().includes(searchTerm)) {
        score += 5;
      }

      // Check keywords match
      if (tool.keywords) {
        for (const keyword of tool.keywords) {
          if (keyword.toLowerCase().includes(searchTerm)) {
            score += 3;
          }
        }
      }

      // Check category match
      if (tool.category && tool.category.toLowerCase().includes(searchTerm)) {
        score += 2;
      }

      if (score > 0) {
        results.push({
          name,
          tool,
          score,
          relevance: score > 10 ? 'high' : score > 5 ? 'medium' : 'low'
        });
      }
    }

    return results.sort((a, b) => b.score - a.score);
  }
}

module.exports = ToolRegistry;
