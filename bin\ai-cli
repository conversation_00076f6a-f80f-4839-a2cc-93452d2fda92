#!/usr/bin/env node

const path = require('path');
const { spawn } = require('child_process');

// Get the directory where this script is located
const binDir = __dirname;
const projectRoot = path.dirname(binDir);
const cliPath = path.join(projectRoot, 'src', 'cli.js');

// Forward all arguments to the main CLI script
const args = process.argv.slice(2);
const child = spawn('node', [cliPath, ...args], {
  stdio: 'inherit',
  cwd: process.cwd()
});

child.on('exit', (code) => {
  process.exit(code);
});

child.on('error', (err) => {
  console.error('Failed to start AI CLI Agent:', err.message);
  process.exit(1);
});
