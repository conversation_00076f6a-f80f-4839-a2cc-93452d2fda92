# AI CLI Agent

🤖 **Autonomous AI-Powered CLI Tool System** with multi-LLM support and intelligent context management.

A production-ready, autonomous AI assistant that lives and breathes in your local environment (Windows 11 WSL, macOS, and Linux). This CLI tool provides intelligent automation, context-aware assistance, and powerful tool execution capabilities.

## ✨ Features

### 🧠 Autonomous Intelligence
- **Autonomous Planning**: Automatically breaks down complex tasks into executable steps
- **Intelligent Decision Making**: Makes smart choices about tool selection and execution flow
- **Parallel Execution**: Runs multiple operations concurrently when appropriate
- **Error Recovery**: Intelligent error handling and automatic recovery mechanisms

### 🔗 Multi-LLM Support
- **OpenAI** (GPT-4, GPT-3.5)
- **Anthropic** (Claude 3 Opus, Sonnet, Haiku)
- **Google** (Gemini Pro)
- **DeepSeek** (<PERSON><PERSON>, <PERSON>r)
- **Mistral** (Large, Medium, Small)
- **Ollama** (Local LLMs)
- **Automatic Fallback**: Seamlessly switches between providers

### 🛠️ Comprehensive Tool System
- **File Operations**: Read, write, create, delete, move, copy, permissions
- **Shell Commands**: Full command-line access with safety controls
- **System Information**: Hardware, software, processes, network details
- **Extensible**: Easy to add custom tools

### 🧭 Context Management
- **Automatic Discovery**: Intelligently indexes your workspace
- **Project Awareness**: Detects project types and configurations
- **Persistent Memory**: Remembers context across sessions
- **Real-time Updates**: Monitors file changes and system state

### 🔒 Security & Safety
- **Permission Controls**: Granular security settings
- **Dangerous Command Detection**: Warns about potentially harmful operations
- **Path Validation**: Prevents directory traversal attacks
- **Confirmation Prompts**: User approval for critical operations

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/ai-cli-agent/ai-cli-agent.git
cd ai-cli-agent

# Install dependencies
npm install

# Make executable globally
npm link

# Run setup wizard
ai-cli config --setup
```

### Basic Usage

```bash
# Interactive chat mode
ai-cli chat

# Execute a task
ai-cli exec "analyze this project and suggest improvements"

# One-shot command with auto-confirmation
ai-cli exec "create a new React component called UserProfile" --yes

# Dry run to see what would be executed
ai-cli exec "refactor the authentication module" --dry-run

# Show system status
ai-cli status

# Get help
ai-cli --help
```

## 📖 Usage Examples

### Interactive Chat
```bash
ai-cli chat
```
Start an interactive conversation with the AI agent. Perfect for exploratory tasks and getting help.

### Task Execution
```bash
# Project analysis
ai-cli exec "analyze the codebase and identify potential security issues"

# Development tasks
ai-cli exec "create a REST API endpoint for user management"
ai-cli exec "add unit tests for the authentication module"
ai-cli exec "optimize the database queries in the user service"

# System administration
ai-cli exec "check system health and generate a report"
ai-cli exec "clean up old log files and temporary directories"

# File operations
ai-cli exec "organize the downloads folder by file type"
ai-cli exec "backup important configuration files"
```

### Advanced Options
```bash
# Parallel execution
ai-cli exec "run tests and build the project" --parallel

# Continue on errors
ai-cli exec "process all files in the directory" --continue-on-error

# Specific provider
ai-cli exec "explain this code" --provider anthropic --model claude-3-opus

# Verbose output
ai-cli exec "deploy to staging" --verbose
```

## ⚙️ Configuration

### Setup Wizard
Run the interactive setup wizard to configure your AI CLI:

```bash
ai-cli config --setup
```

### Manual Configuration
Configuration is stored in `~/.ai-cli/config.yaml`:

```yaml
llm:
  primaryProvider: openai
  fallbackProviders: [anthropic, ollama]
  openai:
    apiKey: your-api-key
    defaultModel: gpt-4
  anthropic:
    apiKey: your-api-key
    defaultModel: claude-3-sonnet-20240229

execution:
  maxConcurrency: 3
  timeout: 300000
  continueOnError: false

security:
  allowFileSystemAccess: true
  allowProcessExecution: true
  confirmDangerous: true
```

### Environment Variables
You can also use environment variables:

```bash
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
export GEMINI_API_KEY="your-gemini-key"
export AI_CLI_LOG_LEVEL="debug"
```

## 🔧 Tools

### File Operations
- Read, write, create, delete files and directories
- Copy, move, and rename operations
- Permission management
- File search and pattern matching
- Safe path validation

### Shell Commands
- Execute any shell command
- Cross-platform compatibility (Windows, macOS, Linux)
- Timeout and output controls
- Dangerous command detection
- Interactive and non-interactive modes

### System Information
- Hardware details (CPU, memory, disk, GPU)
- Software inventory
- Process monitoring
- Network configuration
- Real-time system metrics

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CLI Interface │    │  Agent System   │    │ LLM Providers   │
│                 │◄──►│                 │◄──►│                 │
│ • Commands      │    │ • Planning      │    │ • OpenAI        │
│ • Interactive   │    │ • Execution     │    │ • Anthropic     │
│ • Configuration │    │ • Decision      │    │ • Gemini        │
└─────────────────┘    └─────────────────┘    │ • DeepSeek      │
                                              │ • Mistral       │
┌─────────────────┐    ┌─────────────────┐    │ • Ollama        │
│ Context Manager │    │ Execution Engine│    └─────────────────┘
│                 │    │                 │
│ • Discovery     │    │ • Parallel Exec │    ┌─────────────────┐
│ • Indexing      │    │ • Dependencies  │    │   Tool System   │
│ • Monitoring    │    │ • Error Handling│◄──►│                 │
│ • Memory        │    │ • Recovery      │    │ • File Ops      │
└─────────────────┘    └─────────────────┘    │ • Shell Cmds    │
                                              │ • System Info   │
                                              │ • Custom Tools  │
                                              └─────────────────┘
```

## 🛡️ Security

### Built-in Protections
- **Path Validation**: Prevents directory traversal attacks
- **Command Filtering**: Detects and warns about dangerous commands
- **Permission Checks**: Validates file and directory access
- **Confirmation Prompts**: User approval for critical operations
- **Sandboxing**: Isolated execution environments

### Security Configuration
```yaml
security:
  allowFileSystemAccess: true
  allowNetworkAccess: true
  allowProcessExecution: true
  restrictedPaths:
    - /etc/passwd
    - /etc/shadow
    - C:\Windows\System32
  requireConfirmation:
    - delete
    - remove
    - format
    - shutdown
```

## 📊 Monitoring & Logging

### Log Files
- **Main Log**: `~/.ai-cli/logs/ai-cli.log`
- **Error Log**: `~/.ai-cli/logs/error.log`
- **Execution Log**: `~/.ai-cli/logs/execution.log`

### Log Management
```bash
# View recent logs
ai-cli logs --tail 100

# Export logs
ai-cli logs --export logs-backup.json

# Clear old logs
ai-cli logs --clear
```

## 🔌 Extending the System

### Custom Tools
Create custom tools by implementing the tool interface:

```javascript
// ai-cli-tools/my-custom-tool.js
class MyCustomTool {
  constructor() {
    this.name = 'my-custom-tool';
    this.description = 'Does something amazing';
    this.schema = {
      type: 'object',
      properties: {
        input: { type: 'string', description: 'Input parameter' }
      },
      required: ['input']
    };
  }

  async execute(parameters) {
    // Your tool logic here
    return { success: true, result: 'Amazing result!' };
  }
}

module.exports = MyCustomTool;
```

### Plugin System
The AI CLI supports plugins for extending functionality:

```bash
# Install a plugin
ai-cli plugin install ai-cli-plugin-docker

# List installed plugins
ai-cli plugin list

# Enable/disable plugins
ai-cli plugin enable docker
ai-cli plugin disable docker
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup
```bash
# Clone and install
git clone https://github.com/ai-cli-agent/ai-cli-agent.git
cd ai-cli-agent
npm install

# Run in development mode
npm run dev

# Run tests
npm test

# Lint code
npm run lint
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.ai-cli-agent.com](https://docs.ai-cli-agent.com)
- **Issues**: [GitHub Issues](https://github.com/ai-cli-agent/ai-cli-agent/issues)
- **Discussions**: [GitHub Discussions](https://github.com/ai-cli-agent/ai-cli-agent/discussions)
- **Discord**: [Join our community](https://discord.gg/ai-cli-agent)

## 🙏 Acknowledgments

- OpenAI for GPT models
- Anthropic for Claude models
- Google for Gemini models
- The open-source community for inspiration and tools

---

**Made with ❤️ by the AI CLI Agent team**
